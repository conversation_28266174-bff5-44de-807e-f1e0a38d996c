# cost tracking system

the aizen dubbing platform includes a comprehensive cost tracking system that monitors and calculates costs for all ai services used during the dubbing process. this system provides real-time cost monitoring, budget management, and detailed analytics.

## overview

the cost tracking system monitors costs for:

-   **assemblyai speech-to-text**: tracks audio duration and applies per-minute pricing
-   **gemini translation**: tracks token usage with different rates for input/output tokens
-   **tts providers**: tracks character count, voice type, and audio generation time
-   **gpu/cpu processing**: tracks processing time for demucs, diarization, and other operations

## key features

### 1. real-time cost tracking

-   automatic cost calculation during job processing
-   thread-safe cost accumulation
-   minimal performance impact (< 1ms per operation)

### 2. pre-execution cost estimation

-   analyzes input audio duration and target languages
-   provides accurate cost predictions before job starts
-   helps with budget planning and decision making

### 3. budget management

-   configurable daily, weekly, monthly, and per-job limits
-   real-time budget monitoring with alerts at 80%, 90%, and 100%
-   optional budget enforcement to stop processing when limits are exceeded

### 4. detailed analytics and reporting

-   cost breakdowns by provider, operation type, and time period
-   trend analysis and efficiency metrics
-   exportable reports in json and csv formats

## configuration

### basic configuration

add cost tracking configuration to your run config file:

```json
{
    "cost_tracking": {
        "enabled": true,
        "cost_estimation": true,
        "daily_budget_limit": 50.0,
        "weekly_budget_limit": 300.0,
        "monthly_budget_limit": 1000.0,
        "per_job_budget_limit": 25.0,
        "budget_enforcement": true,
        "alert_thresholds": [0.8, 0.9, 1.0],
        "detailed_reporting": true,
        "export_cost_data": true
    }
}
```

### command line options

enable cost tracking features via command line:

```bash
# basic cost tracking
python -m src.core.main --input_file video.mp4 --target_language es

# with budget limits
python -m src.core.main --input_file video.mp4 --target_language es \
  --daily_budget 50.0 --job_budget 10.0

# with cost estimation disabled
python -m src.core.main --input_file video.mp4 --target_language es \
  --no-cost_estimation

# with cost reporting
python -m src.core.main --input_file video.mp4 --target_language es \
  --export_cost_data --cost_report
```

### custom pricing configuration

create a custom pricing configuration file:

```json
{
    "assemblyai": {
        "provider": "assemblyai",
        "pricing_model": "per_minute",
        "base_cost": "0.37",
        "tier_multipliers": {
            "best": "1.0",
            "nano": "0.27"
        }
    },
    "gemini": {
        "provider": "gemini",
        "pricing_model": "per_token",
        "base_cost": "0.000125",
        "tier_multipliers": {
            "gemini-pro": "1.0",
            "gemini-ultra": "3.0"
        }
    },
    "azure_tts": {
        "provider": "azure_tts",
        "pricing_model": "per_character",
        "base_cost": "0.000016",
        "tier_multipliers": {
            "standard": "1.0",
            "neural": "2.0"
        }
    }
}
```

use custom pricing:

```bash
python -m src.core.main --input_file video.mp4 --target_language es \
  --pricing_config custom_pricing.json
```

## usage examples

### 1. basic cost tracking

```python
from src.config import RunConfig, CostTrackingConfig
from src.cost_tracking.integration import create_cost_integration

# create configuration
config = RunConfig(
    # ... other config ...
    cost_tracking=CostTrackingConfig(
        enabled=True,
        cost_estimation=True,
        daily_budget_limit=50.0,
    )
)

# create cost integration
cost_integration = create_cost_integration(config, "my_run_id")

# estimate costs before processing
estimate = cost_integration.estimate_job_cost(
    input_file="video.mp4",
    target_languages=["es", "fr"],
    tts_provider="azure"
)

print(f"estimated cost: ${estimate['total_estimated_cost']:.2f}")

# ... run dubbing process ...

# finalize costs after processing
final_costs = cost_integration.finalize_job_costs()
print(f"actual cost: ${final_costs['total_cost']:.2f}")
```

### 2. budget management

```python
from src.cost_tracking.budget import BudgetManager, BudgetConfig
from decimal import Decimal

# create budget configuration
budget_config = BudgetConfig(
    daily_limit=Decimal("50.00"),
    weekly_limit=Decimal("300.00"),
    alert_thresholds=[0.8, 0.9, 1.0],
    enforcement_enabled=True,
)

# create budget manager
budget_manager = BudgetManager(budget_config)

# check budget before operation
can_proceed, alerts = budget_manager.check_budget_before_operation(
    Decimal("15.00"), "my_run_id"
)

if not can_proceed:
    print("operation would exceed budget limits")
    for alert in alerts:
        print(f"alert: {alert.get_message()}")
else:
    print("operation approved, within budget")
```

### 3. cost analytics

```python
from src.cost_tracking.analytics import CostAnalytics
from datetime import datetime, timedelta

# create analytics instance
analytics = CostAnalytics()

# generate cost report for last 30 days
end_date = datetime.now()
start_date = end_date - timedelta(days=30)

report = analytics.generate_report(
    cost_entries=all_cost_entries,
    start_date=start_date,
    end_date=end_date,
)

print(f"total cost: ${report.total_cost:.2f}")
print(f"average job cost: ${report.average_job_cost:.2f}")

# analyze cost trends
trends = analytics.analyze_cost_trends(all_cost_entries, days=30)
print(f"cost trend: {trends['trend_direction']}")
print(f"week-over-week change: {trends['week_over_week_change_percent']:.1f}%")

# export report
report.export_to_csv("cost_report.csv")
report.export_to_json("cost_report.json")
```

### 4. custom cost calculators

```python
from src.cost_tracking.calculators import AssemblyAICostCalculator
from src.cost_tracking.models import PricingConfig, ProviderType, PricingModel
from decimal import Decimal

# create custom pricing configuration
pricing_config = PricingConfig(
    provider=ProviderType.ASSEMBLYAI,
    pricing_model=PricingModel.PER_MINUTE,
    base_cost=Decimal("0.40"),  # custom rate
    tier_multipliers={
        "best": Decimal("1.0"),
        "premium": Decimal("1.5"),
    }
)

# create calculator
calculator = AssemblyAICostCalculator(pricing_config)

# calculate cost
cost_entry = calculator.calculate_cost(
    audio_duration_minutes=5.0,
    model_name="premium",
    features=["speaker_labels", "sentiment_analysis"]
)

print(f"cost: ${cost_entry.amount:.4f}")
print(f"unit cost: ${cost_entry.unit_cost:.4f} per minute")
```

## cost breakdown

### assemblyai costs

-   **base rate**: $0.37 per minute for "best" model
-   **nano model**: $0.10 per minute (27% of base rate)
-   **features**: additional costs for speaker labels, sentiment analysis, etc.

### gemini translation costs

-   **input tokens**: $0.125 per 1k tokens
-   **output tokens**: ~2.5x input token cost
-   **model variants**: gemini-ultra costs 3x more than gemini-pro

### tts provider costs

-   **azure**: $16 per 1m characters (standard), $32 per 1m (neural)
-   **elevenlabs**: $30 per 1m characters (premium voices)
-   **yandex**: $15 per 1m characters

### processing costs

-   **gpu processing**: ~$10/hour for high-end gpu
-   **cpu processing**: ~$3/hour for high-end cpu
-   **operation multipliers**: demucs (1.5x), diarization (1.2x)

## monitoring and alerts

### budget alerts

the system generates alerts when spending approaches configured thresholds:

-   **80% threshold**: warning alert
-   **90% threshold**: critical alert
-   **100% threshold**: budget exceeded alert

### cost monitoring

real-time monitoring includes:

-   current job cost
-   daily/weekly/monthly spending
-   cost per provider
-   processing time costs

### reporting

automated reports include:

-   cost breakdowns by provider and operation
-   trend analysis and efficiency metrics
-   budget utilization and variance analysis
-   exportable data for accounting systems

## best practices

### 1. set appropriate budgets

-   start with conservative daily limits
-   monitor actual costs for a few jobs
-   adjust limits based on usage patterns

### 2. use cost estimation

-   always estimate costs before processing
-   compare estimates to actual costs
-   refine estimation accuracy over time

### 3. monitor trends

-   review weekly cost reports
-   identify cost optimization opportunities
-   track efficiency improvements

### 4. optimize for cost

-   use appropriate model tiers (nano vs best)
-   batch similar jobs together
-   consider processing time vs quality tradeoffs

## troubleshooting

### cost tracking not working

1. verify cost tracking is enabled in configuration
2. check that api keys are properly configured
3. ensure run_id is passed to all operations

### inaccurate cost estimates

1. verify audio duration detection is working
2. check pricing configuration is up to date
3. review actual vs estimated cost variance

### budget alerts not triggering

1. verify budget limits are configured
2. check alert thresholds are set correctly
3. ensure budget manager is initialized

### performance issues

1. cost tracking should add < 1ms per operation
2. check for thread contention in high-concurrency scenarios
3. verify cost data storage is not filling up disk space

## example configurations

see the `examples/` directory for complete configuration examples:

-   `cost_tracking_examples.json`: complete run configurations for different scenarios
-   `pricing_configurations.json`: custom pricing configurations for different providers
-   `budget_configurations.json`: budget management examples

## api reference

see the following modules for detailed api documentation:

-   `src.cost_tracking.models`: data models and types
-   `src.cost_tracking.calculators`: cost calculation logic
-   `src.cost_tracking.middleware`: cost tracking middleware
-   `src.cost_tracking.budget`: budget management
-   `src.cost_tracking.analytics`: cost analytics and reporting
-   `src.cost_tracking.integration`: main integration interface

## changelog

### version 1.0.0

-   initial cost tracking system implementation
-   support for assemblyai, gemini, and tts providers
-   budget management and alerts
-   cost analytics and reporting
-   gpu/cpu processing cost tracking
-   comprehensive test coverage
