"""processing cost tracking for gpu/cpu intensive operations.

this module provides specialized cost tracking for demucs audio separation,
pyannote diarization, and other processing operations with multi-level time tracking.
"""

import time
import functools
from contextlib import contextmanager
from typing import Any, Callable, Dict, Optional, TypeVar

from src.utils.logger import logger
from .middleware import get_cost_tracker
from .models import ProviderType, CostType
from .calculators import ProcessingCostCalculator
from .pricing_config import get_pricing_config

F = TypeVar('F', bound=Callable[..., Any])


class ProcessingTimeTracker:
    """tracks processing time and calculates costs for gpu/cpu operations."""
    
    def __init__(self, operation_type: str, device_type: str = "gpu"):
        self.operation_type = operation_type
        self.device_type = device_type
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.cost_calculator = None
        self._initialize_cost_calculator()
    
    def _initialize_cost_calculator(self):
        """initialize cost calculator for processing operations."""
        try:
            provider = ProviderType.GPU_PROCESSING if self.device_type == "gpu" else ProviderType.CPU_PROCESSING
            pricing_config = get_pricing_config(provider)
            self.cost_calculator = ProcessingCostCalculator(pricing_config)
            logger().debug(f"initialized {self.device_type} processing cost tracking for {self.operation_type}")
        except Exception as e:
            logger().warning(f"failed to initialize processing cost tracking: {e}")
            self.cost_calculator = None
    
    def start(self):
        """start timing the operation."""
        self.start_time = time.time()
        logger().debug(f"started timing {self.operation_type} operation")
    
    def stop(self, run_id: Optional[str] = None):
        """stop timing and track cost."""
        self.end_time = time.time()
        
        if self.start_time is None:
            logger().warning(f"stop called without start for {self.operation_type}")
            return 0.0
        
        processing_time = self.end_time - self.start_time
        logger().info(f"{self.operation_type} completed in {processing_time:.2f} seconds")
        
        # track cost if we have a run_id and calculator
        if run_id and self.cost_calculator:
            self._track_cost(processing_time, run_id)
        
        return processing_time
    
    def _track_cost(self, processing_time_seconds: float, run_id: str):
        """track cost for the processing operation."""
        try:
            cost_entry = self.cost_calculator.calculate_cost(
                processing_time_seconds=processing_time_seconds,
                operation_type=self.operation_type,
                device_type=self.device_type,
            )
            
            # add run_id and timing info to metadata
            cost_entry.metadata["run_id"] = run_id
            cost_entry.metadata["start_time"] = self.start_time
            cost_entry.metadata["end_time"] = self.end_time
            cost_entry.metadata["processing_time_seconds"] = processing_time_seconds
            
            # track cost
            tracker = get_cost_tracker(run_id)
            if tracker:
                tracker.add_cost_entry(cost_entry)
                logger().debug(
                    f"tracked {self.device_type} processing cost: ${cost_entry.amount:.4f} "
                    f"for {processing_time_seconds:.2f} seconds of {self.operation_type}"
                )
            
        except Exception as e:
            logger().warning(f"failed to track processing cost: {e}")


@contextmanager
def processing_cost_context(operation_type: str, device_type: str = "gpu", run_id: Optional[str] = None):
    """context manager for tracking processing costs."""
    tracker = ProcessingTimeTracker(operation_type, device_type)
    tracker.start()
    try:
        yield tracker
    finally:
        tracker.stop(run_id)


def track_processing_cost(
    operation_type: str,
    device_type: str = "gpu",
    run_id_param: str = "run_id",
):
    """
    decorator for tracking processing costs.
    
    args:
        operation_type: type of operation (demucs, diarization, etc.)
        device_type: gpu or cpu
        run_id_param: parameter name that contains the run_id
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # extract run_id
            run_id = kwargs.get(run_id_param)
            if not run_id and hasattr(args[0], 'run_id'):
                run_id = getattr(args[0], 'run_id')
            
            # create tracker and time the operation
            tracker = ProcessingTimeTracker(operation_type, device_type)
            tracker.start()
            
            try:
                result = func(*args, **kwargs)
                processing_time = tracker.stop(run_id)
                
                # add processing time to result if it's a dict or has attributes
                if isinstance(result, dict):
                    result["processing_time"] = processing_time
                elif hasattr(result, '__dict__'):
                    setattr(result, 'processing_time', processing_time)
                
                return result
                
            except Exception as e:
                tracker.stop(run_id)  # still track time even on failure
                raise
        
        return wrapper
    
    return decorator


def track_demucs_cost(run_id_param: str = "run_id"):
    """decorator specifically for demucs audio separation cost tracking."""
    return track_processing_cost("demucs", "gpu", run_id_param)


def track_diarization_cost(device_type: str = "gpu", run_id_param: str = "run_id"):
    """decorator specifically for pyannote diarization cost tracking."""
    return track_processing_cost("diarization", device_type, run_id_param)


def track_video_processing_cost(device_type: str = "cpu", run_id_param: str = "run_id"):
    """decorator specifically for video processing cost tracking."""
    return track_processing_cost("video_processing", device_type, run_id_param)


class MultiLevelTimeTracker:
    """tracks processing time at multiple levels (second/minute/hour per task/video)."""
    
    def __init__(self, run_id: str):
        self.run_id = run_id
        self.task_times: Dict[str, float] = {}
        self.video_start_time: Optional[float] = None
        self.video_end_time: Optional[float] = None
    
    def start_video_processing(self):
        """start timing the entire video processing."""
        self.video_start_time = time.time()
        logger().debug(f"started video processing timing for run {self.run_id}")
    
    def end_video_processing(self):
        """end timing the entire video processing."""
        self.video_end_time = time.time()
        if self.video_start_time:
            total_time = self.video_end_time - self.video_start_time
            logger().info(f"total video processing time: {total_time:.2f} seconds ({total_time/60:.2f} minutes)")
            return total_time
        return 0.0
    
    def track_task_time(self, task_name: str, processing_time: float):
        """track time for a specific task."""
        self.task_times[task_name] = processing_time
        logger().debug(f"tracked {task_name}: {processing_time:.2f} seconds")
    
    def get_time_breakdown(self) -> Dict[str, Any]:
        """get comprehensive time breakdown."""
        total_video_time = 0.0
        if self.video_start_time and self.video_end_time:
            total_video_time = self.video_end_time - self.video_start_time
        
        total_task_time = sum(self.task_times.values())
        
        return {
            "run_id": self.run_id,
            "total_video_time_seconds": total_video_time,
            "total_video_time_minutes": total_video_time / 60,
            "total_video_time_hours": total_video_time / 3600,
            "total_task_time_seconds": total_task_time,
            "total_task_time_minutes": total_task_time / 60,
            "total_task_time_hours": total_task_time / 3600,
            "task_breakdown": self.task_times,
            "overhead_time_seconds": max(0, total_video_time - total_task_time),
            "efficiency_ratio": total_task_time / total_video_time if total_video_time > 0 else 0,
        }


# global multi-level trackers
_multi_level_trackers: Dict[str, MultiLevelTimeTracker] = {}


def get_multi_level_tracker(run_id: str) -> MultiLevelTimeTracker:
    """get or create multi-level time tracker for a run."""
    if run_id not in _multi_level_trackers:
        _multi_level_trackers[run_id] = MultiLevelTimeTracker(run_id)
    return _multi_level_trackers[run_id]


def remove_multi_level_tracker(run_id: str) -> Optional[Dict[str, Any]]:
    """remove and return final time breakdown for a run."""
    if run_id in _multi_level_trackers:
        tracker = _multi_level_trackers.pop(run_id)
        return tracker.get_time_breakdown()
    return None


@contextmanager
def multi_level_timing_context(run_id: str):
    """context manager for multi-level time tracking."""
    tracker = get_multi_level_tracker(run_id)
    tracker.start_video_processing()
    try:
        yield tracker
    finally:
        tracker.end_video_processing()
        # keep tracker alive for final breakdown retrieval
