"""cost estimation system for dubbing jobs.

this module provides cost estimation functionality that analyzes input audio
duration and target languages to provide accurate cost predictions before
job execution starts.
"""

import os
from decimal import Decimal
from pathlib import Path
from typing import List, Dict, Any, Optional

from src.utils.logger import logger
from .models import CostEstimate, ProviderType
from .pricing_config import get_pricing_config
from .calculators import (
    AssemblyAICostCalculator,
    GeminiCostCalculator,
    TTSCostCalculator,
    ProcessingCostCalculator,
)


class CostEstimator:
    """estimates costs for dubbing jobs before execution."""
    
    def __init__(self):
        self._calculators = self._initialize_calculators()
    
    def estimate_job_cost(
        self,
        input_file: str,
        target_languages: List[str],
        tts_provider: str = "azure",
        audio_quality: str = "standard",
        voice_type: str = "neural",
        model_config: Optional[Dict[str, Any]] = None,
    ) -> CostEstimate:
        """
        estimate total cost for a dubbing job.
        
        args:
            input_file: path to input video/audio file
            target_languages: list of target language codes
            tts_provider: tts provider to use (azure, elevenlabs, yandex)
            audio_quality: audio quality level
            voice_type: voice type for tts
            model_config: additional model configuration
        """
        try:
            # get audio duration
            audio_duration_minutes = self._get_audio_duration(input_file)
            if audio_duration_minutes <= 0:
                logger().warning(f"could not determine audio duration for {input_file}")
                audio_duration_minutes = 5.0  # fallback estimate
            
            # estimate text length (rough approximation)
            estimated_text_length = self._estimate_text_length(audio_duration_minutes)
            
            # create cost estimate
            estimate = CostEstimate(
                estimated_audio_duration_minutes=audio_duration_minutes,
                target_languages=target_languages,
            )
            
            # estimate assemblyai costs
            estimate.estimated_assemblyai_cost = self._estimate_assemblyai_cost(
                audio_duration_minutes, model_config
            )
            
            # estimate gemini translation costs
            estimate.estimated_gemini_cost = self._estimate_gemini_cost(
                estimated_text_length, len(target_languages), model_config
            )
            
            # estimate tts costs
            estimate.estimated_tts_cost = self._estimate_tts_cost(
                estimated_text_length, target_languages, tts_provider, voice_type, audio_quality
            )
            
            # estimate processing costs
            estimate.estimated_processing_cost = self._estimate_processing_cost(
                audio_duration_minutes, model_config
            )
            
            # calculate total
            estimate.calculate_total()
            
            logger().info(
                f"estimated cost for {input_file}: ${estimate.total_estimated_cost:.4f} "
                f"({audio_duration_minutes:.1f} min, {len(target_languages)} languages)"
            )
            
            return estimate
            
        except Exception as e:
            logger().error(f"error estimating cost for {input_file}: {e}")
            # return minimal estimate
            return CostEstimate(
                estimated_audio_duration_minutes=5.0,
                target_languages=target_languages,
                total_estimated_cost=Decimal("1.00"),
                confidence_level=0.1,
            )
    
    def _get_audio_duration(self, input_file: str) -> float:
        """get audio duration in minutes from input file."""
        try:
            # try to get duration using ffprobe
            import subprocess
            
            cmd = [
                "ffprobe",
                "-v", "quiet",
                "-show_entries", "format=duration",
                "-of", "csv=p=0",
                input_file
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0 and result.stdout.strip():
                duration_seconds = float(result.stdout.strip())
                return duration_seconds / 60.0
            
        except Exception as e:
            logger().debug(f"ffprobe failed for {input_file}: {e}")
        
        try:
            # fallback: estimate based on file size
            file_size_mb = os.path.getsize(input_file) / (1024 * 1024)
            # rough estimate: 1 minute of video ≈ 10-50mb depending on quality
            estimated_minutes = file_size_mb / 25  # conservative estimate
            return max(estimated_minutes, 1.0)  # minimum 1 minute
            
        except Exception as e:
            logger().warning(f"could not estimate duration for {input_file}: {e}")
            return 5.0  # fallback estimate
    
    def _estimate_text_length(self, audio_duration_minutes: float) -> int:
        """estimate text length based on audio duration."""
        # average speaking rate: ~150-200 words per minute
        # average word length: ~5 characters
        # so roughly 750-1000 characters per minute
        words_per_minute = 175  # conservative estimate
        chars_per_word = 5
        return int(audio_duration_minutes * words_per_minute * chars_per_word)
    
    def _estimate_assemblyai_cost(
        self, audio_duration_minutes: float, model_config: Optional[Dict[str, Any]]
    ) -> Decimal:
        """estimate assemblyai transcription cost."""
        try:
            calculator = self._calculators[ProviderType.ASSEMBLYAI]
            model_name = "best"
            features = ["speaker_labels"]  # default features
            
            if model_config:
                model_name = model_config.get("assemblyai_model", model_name)
                if model_config.get("enhanced_features", False):
                    features.extend(["sentiment_analysis", "entity_detection"])
            
            cost_entry = calculator.calculate_cost(
                audio_duration_minutes=audio_duration_minutes,
                model_name=model_name,
                features=features,
            )
            
            return cost_entry.amount
            
        except Exception as e:
            logger().warning(f"error estimating assemblyai cost: {e}")
            # fallback calculation
            return Decimal(str(audio_duration_minutes * 0.37))  # $0.37 per minute
    
    def _estimate_gemini_cost(
        self, text_length: int, num_languages: int, model_config: Optional[Dict[str, Any]]
    ) -> Decimal:
        """estimate gemini translation cost."""
        try:
            calculator = self._calculators[ProviderType.GEMINI]
            
            # estimate tokens (rough approximation: 1 token ≈ 4 characters)
            input_tokens = text_length // 4
            # output is typically similar length for translation
            output_tokens = input_tokens
            
            # multiply by number of target languages
            total_input_tokens = input_tokens * num_languages
            total_output_tokens = output_tokens * num_languages
            
            model_name = "gemini-pro"
            if model_config:
                model_name = model_config.get("gemini_model", model_name)
            
            cost_entry = calculator.calculate_cost(
                input_tokens=total_input_tokens,
                output_tokens=total_output_tokens,
                model_name=model_name,
            )
            
            return cost_entry.amount
            
        except Exception as e:
            logger().warning(f"error estimating gemini cost: {e}")
            # fallback calculation
            tokens = text_length // 4 * num_languages * 2  # input + output
            return Decimal(str(tokens * 0.000125))  # $0.125 per 1k tokens
    
    def _estimate_tts_cost(
        self,
        text_length: int,
        target_languages: List[str],
        tts_provider: str,
        voice_type: str,
        audio_quality: str,
    ) -> Decimal:
        """estimate tts synthesis cost."""
        try:
            # map provider names to types
            provider_map = {
                "azure": ProviderType.AZURE_TTS,
                "elevenlabs": ProviderType.ELEVENLABS_TTS,
                "yandex": ProviderType.YANDEX_TTS,
            }
            
            provider_type = provider_map.get(tts_provider, ProviderType.AZURE_TTS)
            calculator = self._calculators[provider_type]
            
            total_cost = Decimal("0")
            
            for language in target_languages:
                cost_entry = calculator.calculate_cost(
                    character_count=text_length,
                    voice_type=voice_type,
                    audio_quality=audio_quality,
                    language=language,
                )
                total_cost += cost_entry.amount
            
            return total_cost
            
        except Exception as e:
            logger().warning(f"error estimating tts cost: {e}")
            # fallback calculation
            cost_per_char = 0.000016  # azure standard rate
            if tts_provider == "elevenlabs":
                cost_per_char = 0.00003
            elif tts_provider == "yandex":
                cost_per_char = 0.000015
            
            return Decimal(str(text_length * len(target_languages) * cost_per_char))
    
    def _estimate_processing_cost(
        self, audio_duration_minutes: float, model_config: Optional[Dict[str, Any]]
    ) -> Decimal:
        """estimate gpu/cpu processing cost."""
        try:
            calculator = self._calculators[ProviderType.GPU_PROCESSING]
            
            # estimate processing time (typically 2-5x real-time for full pipeline)
            processing_time_multiplier = 3.0  # conservative estimate
            processing_time_seconds = audio_duration_minutes * 60 * processing_time_multiplier
            
            # estimate different operations
            operations = [
                ("demucs", processing_time_seconds * 0.4),  # audio separation
                ("diarization", processing_time_seconds * 0.3),  # speaker diarization
                ("video_processing", processing_time_seconds * 0.3),  # video processing
            ]
            
            total_cost = Decimal("0")
            for operation_type, duration in operations:
                cost_entry = calculator.calculate_cost(
                    processing_time_seconds=duration,
                    operation_type=operation_type,
                    device_type="gpu",
                )
                total_cost += cost_entry.amount
            
            return total_cost
            
        except Exception as e:
            logger().warning(f"error estimating processing cost: {e}")
            # fallback calculation
            processing_minutes = audio_duration_minutes * 3  # 3x real-time
            return Decimal(str(processing_minutes * 0.17))  # ~$10/hour gpu
    
    def _initialize_calculators(self) -> Dict[ProviderType, Any]:
        """initialize cost calculators for all providers."""
        calculators = {}
        
        for provider in ProviderType:
            try:
                pricing_config = get_pricing_config(provider)
                
                if provider == ProviderType.ASSEMBLYAI:
                    calculators[provider] = AssemblyAICostCalculator(pricing_config)
                elif provider == ProviderType.GEMINI:
                    calculators[provider] = GeminiCostCalculator(pricing_config)
                elif provider in [ProviderType.AZURE_TTS, ProviderType.ELEVENLABS_TTS, ProviderType.YANDEX_TTS]:
                    calculators[provider] = TTSCostCalculator(pricing_config)
                elif provider in [ProviderType.GPU_PROCESSING, ProviderType.CPU_PROCESSING]:
                    calculators[provider] = ProcessingCostCalculator(pricing_config)
                
            except Exception as e:
                logger().warning(f"failed to initialize calculator for {provider.value}: {e}")
        
        return calculators
