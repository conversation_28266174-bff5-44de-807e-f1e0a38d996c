"""cost tracking integration for the dubbing pipeline.

this module provides the main integration point for cost tracking, tying together
all cost tracking components and integrating with the dubbing pipeline.
"""

import json
from decimal import Decimal
from pathlib import Path
from typing import Optional, Dict, Any

from src.utils.logger import logger
from src.utils.run_manager import RunManager
from src.config import RunConfig
from .middleware import create_cost_tracker, get_cost_tracker, remove_cost_tracker
from .models import ProviderType, BudgetConfig
from .estimator import CostEstimator
from .budget import BudgetManager, BudgetAlert
from .analytics import CostAnalytics
from .pricing_config import get_pricing_manager
from .calculators import (
    AssemblyAICostCalculator,
    GeminiCostCalculator,
    TTSCostCalculator,
    ProcessingCostCalculator,
)


class CostTrackingIntegration:
    """main integration class for cost tracking in the dubbing pipeline."""
    
    def __init__(self, config: RunConfig, run_id: str):
        self.config = config
        self.run_id = run_id
        self.cost_estimator = CostEstimator()
        self.budget_manager: Optional[BudgetManager] = None
        self.cost_analytics = CostAnalytics()
        self.run_manager = RunManager()
        
        # initialize if cost tracking is enabled
        if config.cost_tracking.enabled:
            self._initialize_cost_tracking()
    
    def _initialize_cost_tracking(self):
        """initialize cost tracking components."""
        try:
            # create cost tracker for this run
            tracker = create_cost_tracker(self.run_id)
            
            # initialize calculators for all providers
            self._setup_calculators(tracker)
            
            # initialize budget manager if budget limits are configured
            if self._has_budget_limits():
                self._setup_budget_manager()
            
            logger().info(f"initialized cost tracking for run {self.run_id}")
            
        except Exception as e:
            logger().error(f"failed to initialize cost tracking: {e}")
            # disable cost tracking on initialization failure
            self.config.cost_tracking.enabled = False
    
    def _setup_calculators(self, tracker):
        """setup cost calculators for all ai providers."""
        pricing_manager = get_pricing_manager()
        
        # setup assemblyai calculator
        assemblyai_config = pricing_manager.get_pricing_config(ProviderType.ASSEMBLYAI)
        tracker.set_calculator(ProviderType.ASSEMBLYAI, AssemblyAICostCalculator(assemblyai_config))
        
        # setup gemini calculator
        gemini_config = pricing_manager.get_pricing_config(ProviderType.GEMINI)
        tracker.set_calculator(ProviderType.GEMINI, GeminiCostCalculator(gemini_config))
        
        # setup tts calculators
        for provider in [ProviderType.AZURE_TTS, ProviderType.ELEVENLABS_TTS, ProviderType.YANDEX_TTS]:
            tts_config = pricing_manager.get_pricing_config(provider)
            tracker.set_calculator(provider, TTSCostCalculator(tts_config))
        
        # setup processing calculators
        for provider in [ProviderType.GPU_PROCESSING, ProviderType.CPU_PROCESSING]:
            processing_config = pricing_manager.get_pricing_config(provider)
            tracker.set_calculator(provider, ProcessingCostCalculator(processing_config))
    
    def _has_budget_limits(self) -> bool:
        """check if any budget limits are configured."""
        ct_config = self.config.cost_tracking
        return any([
            ct_config.daily_budget_limit,
            ct_config.weekly_budget_limit,
            ct_config.monthly_budget_limit,
            ct_config.per_job_budget_limit,
        ])
    
    def _setup_budget_manager(self):
        """setup budget manager with configured limits."""
        ct_config = self.config.cost_tracking
        
        budget_config = BudgetConfig(
            daily_limit=Decimal(str(ct_config.daily_budget_limit)) if ct_config.daily_budget_limit else None,
            weekly_limit=Decimal(str(ct_config.weekly_budget_limit)) if ct_config.weekly_budget_limit else None,
            monthly_limit=Decimal(str(ct_config.monthly_budget_limit)) if ct_config.monthly_budget_limit else None,
            per_job_limit=Decimal(str(ct_config.per_job_budget_limit)) if ct_config.per_job_budget_limit else None,
            alert_thresholds=ct_config.alert_thresholds,
            enforcement_enabled=ct_config.budget_enforcement,
            notification_emails=ct_config.notification_emails,
        )
        
        self.budget_manager = BudgetManager(budget_config)
        self.budget_manager.add_alert_callback(self._handle_budget_alert)
        
        logger().info("initialized budget manager with configured limits")
    
    def _handle_budget_alert(self, alert: BudgetAlert):
        """handle budget alert notifications."""
        logger().warning(f"budget alert: {alert.get_message()}")
        
        # log alert details
        alert_data = alert.to_dict()
        logger().info(f"budget alert details: {json.dumps(alert_data, indent=2)}")
        
        # if enforcement is enabled and budget is exceeded, we could stop processing here
        # but that would require integration with the main processing loop
    
    def estimate_job_cost(
        self,
        input_file: str,
        target_languages: list[str],
        tts_provider: str = "azure",
    ) -> Optional[Dict[str, Any]]:
        """estimate cost for a dubbing job before execution."""
        if not self.config.cost_tracking.enabled or not self.config.cost_tracking.cost_estimation:
            return None
        
        try:
            estimate = self.cost_estimator.estimate_job_cost(
                input_file=input_file,
                target_languages=target_languages,
                tts_provider=tts_provider,
            )
            
            # store estimate in run manager
            self.run_manager.update_run_costs(
                run_id=self.run_id,
                estimated_cost=float(estimate.total_estimated_cost),
            )
            
            # check budget before proceeding if budget manager is available
            if self.budget_manager:
                can_proceed, alerts = self.budget_manager.check_budget_before_operation(
                    estimate.total_estimated_cost, self.run_id
                )
                
                if not can_proceed:
                    logger().error("job cannot proceed due to budget limits")
                    for alert in alerts:
                        logger().error(alert.get_message())
                    return None
            
            estimate_dict = estimate.dict()
            logger().info(f"estimated job cost: ${estimate.total_estimated_cost:.4f}")
            return estimate_dict
            
        except Exception as e:
            logger().error(f"failed to estimate job cost: {e}")
            return None
    
    def finalize_job_costs(self) -> Optional[Dict[str, Any]]:
        """finalize cost tracking when job completes."""
        if not self.config.cost_tracking.enabled:
            return None
        
        try:
            tracker = get_cost_tracker(self.run_id)
            if not tracker:
                logger().warning(f"no cost tracker found for run {self.run_id}")
                return None
            
            # get final cost summary
            cost_summary = tracker.get_cost_summary()
            
            # update run manager with final costs
            self.run_manager.update_run_costs(
                run_id=self.run_id,
                actual_cost=cost_summary["total_cost"],
                cost_breakdown=cost_summary,
                processing_duration=cost_summary.get("duration"),
            )
            
            # export cost data if configured
            if self.config.cost_tracking.export_cost_data:
                self._export_cost_data(cost_summary)
            
            # generate cost report if configured
            if self.config.cost_tracking.detailed_reporting:
                self._generate_cost_report()
            
            # cleanup cost tracker
            remove_cost_tracker(self.run_id)
            
            logger().info(f"finalized cost tracking for run {self.run_id}: ${cost_summary['total_cost']:.4f}")
            return cost_summary
            
        except Exception as e:
            logger().error(f"failed to finalize job costs: {e}")
            return None
    
    def _export_cost_data(self, cost_summary: Dict[str, Any]):
        """export cost data to file."""
        try:
            export_dir = Path("data/cost_exports")
            export_dir.mkdir(parents=True, exist_ok=True)
            
            export_file = export_dir / f"cost_data_{self.run_id}.json"
            with open(export_file, 'w') as f:
                json.dump(cost_summary, f, indent=2, default=str)
            
            logger().info(f"exported cost data to {export_file}")
            
        except Exception as e:
            logger().error(f"failed to export cost data: {e}")
    
    def _generate_cost_report(self):
        """generate detailed cost report."""
        try:
            tracker = get_cost_tracker(self.run_id)
            if not tracker:
                return
            
            # collect all cost entries
            all_entries = []
            all_entries.extend(tracker.cost_breakdown.assemblyai_costs)
            all_entries.extend(tracker.cost_breakdown.gemini_costs)
            all_entries.extend(tracker.cost_breakdown.tts_costs)
            all_entries.extend(tracker.cost_breakdown.processing_costs)
            
            if not all_entries:
                logger().info("no cost entries found for report generation")
                return
            
            # generate report
            report = self.cost_analytics.generate_report(all_entries)
            
            # save report
            report_dir = Path("data/cost_reports")
            report_dir.mkdir(parents=True, exist_ok=True)
            
            report_file = report_dir / f"cost_report_{self.run_id}.json"
            report.export_to_json(report_file)
            
            # also save as csv
            csv_file = report_dir / f"cost_report_{self.run_id}.csv"
            report.export_to_csv(csv_file)
            
            logger().info(f"generated cost report: {report_file}")
            
        except Exception as e:
            logger().error(f"failed to generate cost report: {e}")
    
    def get_current_cost(self) -> float:
        """get current total cost for the job."""
        if not self.config.cost_tracking.enabled:
            return 0.0
        
        tracker = get_cost_tracker(self.run_id)
        if tracker:
            return tracker.get_total_cost()
        return 0.0
    
    def get_budget_status(self) -> Optional[Dict[str, Any]]:
        """get current budget status."""
        if self.budget_manager:
            return self.budget_manager.get_budget_status()
        return None


def create_cost_integration(config: RunConfig, run_id: str) -> CostTrackingIntegration:
    """create cost tracking integration for a dubbing run."""
    return CostTrackingIntegration(config, run_id)
