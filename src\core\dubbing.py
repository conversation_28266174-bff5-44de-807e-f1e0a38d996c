import asyncio
import dataclasses
import functools
import logging
import os
import re
import shutil
import sys
import time
import uuid
from datetime import datetime

from typing import Final, Mapping, TYPE_CHECKING, Union

import psutil

from pyannote.audio import Pipeline

from src.utils.logger import logger
from src.audio_processing import audio_processing
from src.audio_processing.demucs import Demucs
from src.video_processing.ffmpeg import FFmpeg
from src.utils.preprocessing import PreprocessingArtifacts

# removed import of base SpeechToText class - now using assemblyai directly
from src.video_processing.subtitles import Subtitles
from src.audio_processing.text_to_speech import TextToSpeech
from src.translation import Translation
from src.utils.utterance import Utterance
from src.video_processing.video_processing import VideoProcessing
from src.utils.run_manager import RunManager, RunInfo

from src.utils.exit_code import ExitCode

# define the default output directory
outputs_dir = "data/outputs/"

_default_pyannote_model: Final[str] = "pyannote/speaker-diarization-3.1"
_number_of_steps: Final[int] = 7

if TYPE_CHECKING:
    from src.config import RunConfig


@dataclasses.dataclass
class PostprocessingArtifacts:
    """instance with postprocessing outputs.

    attributes:
        audio_file: a path to a dubbed audio file.
        video_file: a path to a dubbed video file. the video is optional.
    """

    audio_file: str
    video_file: str | None


class PyAnnoteAccessError(Exception):
    """error when establishing access to pyannore from hugging face."""

    pass


def rename_input_file(original_input_file: str) -> str:
    """converts a filename to lowercase letters and numbers only, preserving the file extension.

    args:
        original_filename: the filename to normalize.

    returns:
        the normalized filename.
    """
    directory, filename = os.path.split(original_input_file)
    base_name, extension = os.path.splitext(filename)
    normalized_name = re.sub(r"[^a-z0-9]", "", base_name.lower())
    return os.path.join(directory, normalized_name + extension)


def overwrite_input_file(input_file: str, updated_input_file: str) -> None:
    """renames a file in place to lowercase letters and numbers only, preserving the file extension."""

    if not os.path.exists(input_file):
        raise FileNotFoundError(f"file '{input_file}' not found.")

    shutil.move(input_file, updated_input_file)


class Dubber:
    """a src to manage the entire ad dubbing process."""

    def __init__(
        self,
        *,
        input_file: str,
        target_language: str,
        source_language: str = None,
        output_directory: str = outputs_dir,
        gender_mapping: Mapping[str, str] = None,
        voice_mapping: Mapping[str, str] = None,
        pyannote_auth_token: str = None,
        device: str = "cpu",
        no_dubbing_phrases: list = None,
        dubbing_mode: str = "match",
        max_words_per_duration: float = 3.0,
        audio_extension: str = ".mp3",
        original_subtitles_filename: str = None,
        original_dubbed_subtitles_filename: str = None,
        embedding_model_name: str = "MiniLM",
        generate_subtitles: bool = True,
        text_to_speech_provider: str = "auto",
        text_to_speech_api_key: str = None,
        video_codec: str = "h264",
        min_silence_duration: float = 0.5,
        silence_threshold: float = -35,
        max_segment_duration: float = 30.0,
        use_enhanced_segmentation: bool = True,
        skip_diarization: bool = False,
        target_language_region: str = "",
        hugging_face_token: str = None,
        tts: TextToSpeech = None,
        translation: Translation = None,
        stt=None,  # assemblyai speech-to-text instance
        cpu_threads: int = 0,
        clean_intermediate_files: bool = False,
        original_subtitles: bool = False,
        dubbed_subtitles: bool = False,
        run_config: Union["RunConfig", None] = None,
    ) -> None:
        """
        initialize the dubber src with configuration parameters.

        args:
            input_file: the path to the video file to dub.
            target_language: the language to dub the video into.
            source_language: the language of the original video.
            output_directory: the base directory to save output files. a unique run-specific
                            subdirectory will be created using a uuid4 identifier.
            gender_mapping: a mapping of gender to voice.
            voice_mapping: a mapping of speaker_id to voice.
            pyannote_auth_token: the huggingface auth token for pyannote.
            device: the device to use for processing.
            no_dubbing_phrases: a list of phrases to not dub.
            dubbing_mode: the dubbing mode to use.
            max_words_per_duration: the maximum words per duration for audio.
            audio_extension: the audio file extension to use.
            original_subtitles_filename: the path to the original subtitles.
            original_dubbed_subtitles_filename: the path to the dubbed subtitles.
            embedding_model_name: the embedding model to use.
            generate_subtitles: whether to generate subtitles.
            text_to_speech_provider: the text-to-speech provider to use.
            text_to_speech_api_key: the text-to-speech api key.
            video_codec: the video codec to use for output video.
            min_silence_duration: minimum duration (seconds) for a silence to be considered a pause
            silence_threshold: silence threshold in db (lower values = more sensitive)
            max_segment_duration: maximum duration (seconds) for any single segment
            use_enhanced_segmentation: whether to use enhanced segmentation with pauses
            skip_diarization: skip speaker diarization to make preprocessing faster
            target_language_region: region code for the target language (e.g., 'es' for spain)
            hugging_face_token: hugging face api token
            tts: text-to-speech instance
            translation: translation instance
            stt: speech-to-text instance
            cpu_threads: number of cpu threads to use
            clean_intermediate_files: whether to clean intermediate files
            original_subtitles: whether to include original language subtitles
            dubbed_subtitles: whether to include dubbed language subtitles
            run_config: optional run configuration

        note:
            each dubbing session generates a unique uuid4 run identifier and creates a
            run-specific output directory to organize all files from that session.

        """
        if voice_mapping is None:
            voice_mapping = {}
        if no_dubbing_phrases is None:
            no_dubbing_phrases = []

        # generate unique run id for this dubbing session
        self.run_id = str(uuid.uuid4())
        logger().info(f"generated run id: {self.run_id}")

        # create run-specific output directory
        self.base_output_directory = output_directory
        self.run_output_directory = os.path.join(
            self.base_output_directory, self.run_id
        )
        os.makedirs(self.run_output_directory, exist_ok=True)
        logger().info(f"created run-specific directory: {self.run_output_directory}")

        # initialize run manager and register this run
        self.run_manager = RunManager(self.base_output_directory)
        run_info = RunInfo(
            run_id=self.run_id,
            created_at=datetime.now().isoformat(),
            input_file=input_file,
            source_language=source_language or "unknown",
            target_language=target_language,
            status="running",
            output_directory=self.run_output_directory,
            files_created=[],
            metadata={},
        )
        self.run_manager.register_run(run_info)

        self._original_input_file = input_file  # store original input file path/url
        self.output_directory = self.run_output_directory  # use run-specific directory
        self.source_language = source_language
        self.target_language = target_language
        self.target_language_region = target_language_region
        self.gender_mapping = gender_mapping
        self.voice_mapping = voice_mapping
        self.device = device
        self.no_dubbing_phrases = no_dubbing_phrases
        self.dubbing_mode = dubbing_mode
        self.max_words_per_duration = max_words_per_duration
        self.audio_extension = audio_extension
        self.original_subtitles_filename = original_subtitles_filename
        self.original_dubbed_subtitles_filename = original_dubbed_subtitles_filename
        self.embedding_model_name = embedding_model_name
        self.generate_subtitles = generate_subtitles
        self.video_codec = video_codec
        self.text_to_speech_provider = text_to_speech_provider
        self.text_to_speech_api_key = text_to_speech_api_key
        self.preprocessing_output = None
        self.utterance_metadata = None
        self.tts = tts
        self.translation = translation
        self.stt = stt
        self.cpu_threads = cpu_threads
        self.clean_intermediate_files = clean_intermediate_files
        self.original_subtitles = original_subtitles
        self.dubbed_subtitles = dubbed_subtitles

        # segmentation parameters
        self.min_silence_duration = min_silence_duration
        self.silence_threshold = silence_threshold
        self.max_segment_duration = max_segment_duration
        self.use_enhanced_segmentation = use_enhanced_segmentation
        self.skip_diarization = skip_diarization

        # -------------------------------------------------------------
        # apply overrides from unified run configuration if provided
        # -------------------------------------------------------------
        if run_config is not None:
            # update segmentation settings if caller left defaults
            if min_silence_duration == 0.5:
                self.min_silence_duration = run_config.input.silence.padding_seconds

            if silence_threshold == -35:
                if run_config.input.silence.method == "energy":
                    self.silence_threshold = run_config.input.silence.threshold * -100
                else:
                    self.silence_threshold = run_config.input.silence.threshold

            if max_segment_duration == 30.0:
                self.max_segment_duration = run_config.input.separation.segment_duration

            # performance overrides
            if cpu_threads == 0:
                self.cpu_threads = run_config.performance.max_workers

            # future: pass entire config to sub-components when refactored

        if not self.skip_diarization:
            pyannote_auth_token = pyannote_auth_token or os.environ.get(
                "pyannote_auth_token"
            )
            self.pyannote_pipeline = Pipeline.from_pretrained(
                "pyannote/speaker-diarization-3.1",
                use_auth_token=pyannote_auth_token,
            )
        else:
            self.pyannote_pipeline = None

        # store unified run configuration for downstream use
        self.run_config = run_config

    @functools.cached_property
    def input_file(self):
        """returns the input file path, handling urls and local files appropriately."""
        # if it's a url, return it as-is without trying to rename
        if self._original_input_file.startswith(
            ("http://", "https://", "ftp://", "ftps://")
        ):
            return self._original_input_file

        # for local files, apply the renaming logic
        renamed_input_file = rename_input_file(self._original_input_file)
        if renamed_input_file != self._original_input_file:
            logger().warning(
                "the input file was renamed because the original name contained"
                " spaces, hyphens, or other incompatible characters. the updated"
                f" input file is: {renamed_input_file}"
            )
            overwrite_input_file(
                input_file=self._original_input_file,
                updated_input_file=renamed_input_file,
            )
        return renamed_input_file

    def get_run_id(self) -> str:
        """returns the unique run identifier for this dubbing session."""
        return self.run_id

    def get_base_output_directory(self) -> str:
        """returns the base output directory (before run-specific subdirectory)."""
        return self.base_output_directory

    def get_run_output_directory(self) -> str:
        """returns the run-specific output directory where all files are saved."""
        return self.run_output_directory

    def get_run_manager(self) -> RunManager:
        """returns the run manager instance."""
        return self.run_manager

    def _update_run_status(self, status: str) -> None:
        """updates the run status and scans for created files."""
        files_created = self.run_manager.scan_run_files(self.run_id)
        self.run_manager.update_run_status(self.run_id, status, files_created)

    def log_maxrss_memory(self):
        """logs the maximum resident set size memory usage."""
        if sys.platform == "win32" or sys.platform == "win64":
            return

        import resource

        max_rss_self = resource.getrusage(resource.rusage_self).ru_maxrss / 1024
        if sys.platform == "darwin":
            return max_rss_self / 1024

        # calculate memory usage in mb
        max_rss_mb = max_rss_self / 1024

        # get total system memory for percentage calculation
        total_memory_mb = psutil.virtual_memory().total / (1024 * 1024)
        max_rss_self_percent = (max_rss_mb / total_memory_mb) * 100

        logger().info(
            f"maximum memory used: {max_rss_mb:.0f} mb ({max_rss_self_percent:.2f}% of total)"
        )

    def log_debug_task_and_getime(self, text, start_time):
        """logs debug information about a task and its execution time."""
        process = psutil.Process(os.getpid())
        current_rss = process.memory_info().rss / 1024**2
        _time = time.time() - start_time
        logger().info(
            f"completed task '{text.lower()}': current_rss {current_rss:.2f} mb, time {_time:.2f}s"
        )
        return _time

    def _verify_api_access(self) -> None:
        """verifies access to all the required apis."""
        logger().debug("verifying access to pyannoté from huggingface.")
        if not self.pyannote_pipeline:
            raise PyAnnoteAccessError(
                "no access to huggingface. make sure you passed the correct api token"
                " either as 'pyannote_auth_token' or through the"
                " environmental variable. also, please make sure you accepted the"
                " user agreement for the segmentation model"
                " (https://huggingface.co/pyannote/segmentation-3.0) and the speaker"
                " diarization model"
                " (https://huggingface.co/pyannote/speaker-diarization-3.1)."
            )
        logger().debug("access to pyannoté from huggingface verified.")

    def run_preprocessing(self) -> None:
        """splits audio/video, applies demucs, and segments audio into utterances with pyannote."""
        video_file, audio_file = VideoProcessing.split_audio_video(
            video_file=self.input_file, output_directory=self.output_directory
        )

        # safety: ensure extracted files are inside the run output directory
        abs_output_dir = os.path.abspath(self.output_directory)

        def _assert_in_output(path: str) -> str:
            if path and not os.path.abspath(path).startswith(abs_output_dir):
                new_path = os.path.join(abs_output_dir, os.path.basename(path))
                try:
                    shutil.move(path, new_path)
                    logger().warning(
                        f"relocated '{path}' into run output directory -> '{new_path}'"
                    )
                    return new_path
                except (OSError, FileNotFoundError) as e:
                    logger().error(
                        f"failed to relocate '{path}' into output directory: {e}"
                    )
            return path

        video_file = _assert_in_output(video_file)
        audio_file = _assert_in_output(audio_file)

        demucs = Demucs()

        # configure separation parameters from unified config if available
        sep_cfg = None
        if self.run_config is not None:
            sep_cfg = self.run_config.input.separation

        demucs_command = demucs.build_demucs_command(
            audio_file=audio_file,
            output_directory=self.output_directory,
            device=self.device,
            segment=int(sep_cfg.segment_duration) if sep_cfg else 6,
            split=True,
            model=sep_cfg.model if sep_cfg else "htdemucs_ft",
            jobs=(self.run_config.performance.max_workers if self.run_config else 0),
        )
        demucs.execute_demucs_command(command=demucs_command)
        audio_vocals_file, audio_background_file = (
            demucs.assemble_split_audio_file_paths(command=demucs_command)
        )

        # use fast segmentation by default if skip_diarization is enabled
        if self.skip_diarization:
            logger().info("skipping speaker diarization for faster processing")
            # use only pause-based segmentation for speed
            pause_segments = audio_processing.detect_natural_pauses(
                audio_file=audio_file,
                min_silence_duration=self.min_silence_duration,
                silence_threshold=self.silence_threshold,
                max_segment_duration=self.max_segment_duration,
            )

            # create simple utterance metadata with a single speaker
            utterance_metadata = []
            for i, segment in enumerate(pause_segments):
                # add a simple speaker id since we're skipping diarization
                segment_with_speaker = segment.copy()
                segment_with_speaker["speaker_id"] = (
                    f"s{i % 2}"  # alternate between 2 speakers
                )
                utterance_metadata.append(segment_with_speaker)

            # process the segments
            utterance_metadata = audio_processing.run_cut_and_save_audio(
                utterance_metadata=utterance_metadata,
                audio_file=audio_file,
                output_directory=self.output_directory,
            )
        else:
            # step 1: get speaker-based diarization segments
            utterance_metadata = audio_processing.create_pyannote_timestamps(
                audio_file=audio_file,
                pipeline=self.pyannote_pipeline,
                device=self.device,
            )

            # only use enhanced segmentation if enabled
            if self.use_enhanced_segmentation:
                # step 2: get pause-based segmentation
                pause_segments = audio_processing.detect_natural_pauses(
                    audio_file=audio_file,
                    min_silence_duration=self.min_silence_duration,
                    silence_threshold=self.silence_threshold,
                    max_segment_duration=self.max_segment_duration,
                )

                # step 3: combine both segmentation approaches
                combined_segments = audio_processing.combine_diarization_and_pauses(
                    diarization_segments=utterance_metadata,
                    pause_segments=pause_segments,
                    max_segment_duration=self.max_segment_duration,
                )

                # use the combined segments for further processing
                utterance_metadata = audio_processing.run_cut_and_save_audio(
                    utterance_metadata=combined_segments,
                    audio_file=audio_file,
                    output_directory=self.output_directory,
                )
            else:
                # use only speaker-based segmentation (original behavior)
                utterance_metadata = audio_processing.run_cut_and_save_audio(
                    utterance_metadata=utterance_metadata,
                    audio_file=audio_file,
                    output_directory=self.output_directory,
                )

        self.utterance_metadata = utterance_metadata
        self.preprocessing_output = PreprocessingArtifacts(
            video_file=video_file,
            audio_file=audio_file,
            audio_vocals_file=audio_vocals_file,
            audio_background_file=audio_background_file,
        )

    def run_speech_to_text(self) -> None:
        """transcribes audio, applies speaker diarization, and updates metadata.

        returns:
            updated utterance metadata with speaker information and transcriptions.
        """

        media_file = (
            self.preprocessing_output.video_file
            if self.preprocessing_output.video_file
            else self.preprocessing_output.audio_file
        )
        utterance_metadata = self.stt.transcribe_audio_chunks(
            utterance_metadata=self.utterance_metadata,
            source_language=self.source_language,
            no_dubbing_phrases=[],
        )
        speaker_info = self.stt.predict_gender(
            file=media_file,
            utterance_metadata=utterance_metadata,
        )
        self.utterance_metadata = self.stt.add_speaker_info(
            utterance_metadata=utterance_metadata, speaker_info=speaker_info
        )

        utterance = Utterance(self.target_language, self.output_directory)
        self.utterance_metadata = utterance.get_without_empty_blocks(
            self.utterance_metadata
        )

    async def run_speech_to_text_async(self) -> None:
        """
        async version of speech-to-text transcription with massive performance improvements.

        uses parallel processing to transcribe multiple audio chunks simultaneously,
        reducing transcription time from 10-20s per minute to 1-3s total.
        """
        logger().info("starting async speech-to-text transcription")

        media_file = (
            self.preprocessing_output.video_file
            if self.preprocessing_output.video_file
            else self.preprocessing_output.audio_file
        )

        # progress tracking callback
        def progress_callback(completed: int, total: int):
            percentage = (completed / total) * 100 if total > 0 else 0
            logger().info(
                f"transcription progress: {completed}/{total} ({percentage:.1f}%)"
            )

        # use async transcription with intelligent batching
        utterance_metadata = await self.stt.transcribe_audio_chunks_async(
            utterance_metadata=self.utterance_metadata,
            source_language=self.source_language,
            no_dubbing_phrases=[],
            max_concurrent=10,  # adjust based on api limits
            progress_callback=progress_callback,
        )

        # predict gender for speakers (this is still synchronous but fast)
        speaker_info = self.stt.predict_gender(
            file=media_file,
            utterance_metadata=utterance_metadata,
        )

        # add speaker information
        self.utterance_metadata = self.stt.add_speaker_info(
            utterance_metadata=utterance_metadata, speaker_info=speaker_info
        )

        # filter out empty blocks
        utterance = Utterance(self.target_language, self.output_directory)
        self.utterance_metadata = utterance.get_without_empty_blocks(
            self.utterance_metadata
        )

        logger().info("async speech-to-text transcription completed")

    def run_translation(self) -> None:
        """translates transcribed text and potentially merges utterances"""

        self.utterance_metadata = self.translation.translate_utterances(
            utterance_metadata=self.utterance_metadata,
            source_language=self.source_language,
            target_language=self.target_language,
        )

    def run_configure_text_to_speech(self) -> None:
        """configures the text-to-speech process.

        returns:
            updated utterance metadata with assigned voices
            and text-to-speech settings.
        """
        # if unified config specifies a preferred voice, apply it to all speakers
        preferred_voice = None
        if self.run_config is not None and self.run_config.models.tts_voice:
            preferred_voice = self.run_config.models.tts_voice

        if preferred_voice:
            logger().info(f"using preferred tts voice from config: {preferred_voice}")
            speaker_ids = {chunk["speaker_id"] for chunk in self.utterance_metadata}
            assigned_voices = {sid: preferred_voice for sid in speaker_ids}
        else:
            assigned_voices = self.tts.assign_voices(
                utterance_metadata=self.utterance_metadata,
                target_language=self.target_language,
                target_language_region=self.target_language_region,
            )

        self.utterance_metadata = self.tts.update_utterance_metadata(
            utterance_metadata=self.utterance_metadata,
            assigned_voices=assigned_voices,
        )

    def run_text_to_speech(self) -> None:
        """converts translated text to speech and dubs utterance"""
        self.utterance_metadata = self.tts.dub_utterances(
            utterance_metadata=self.utterance_metadata,
            output_directory=self.output_directory,
            target_language=self.target_language,
            audio_file=self.preprocessing_output.audio_file,
        )

    def run_cleaning(self) -> None:
        """cleans up intermediate files if enabled."""
        if not self.clean_intermediate_files:
            return

        output_directory = None
        paths, dubbed_paths = Utterance(
            self.target_language, self.output_directory
        ).get_files_paths(self.utterance_metadata)
        for path in paths + dubbed_paths:
            if os.path.exists(path):
                os.remove(path)
            if not output_directory:
                output_directory = os.path.dirname(path)

        if output_directory:
            for path in [
                f"dubbed_audio_{self.target_language}.mp3",
                "dubbed_vocals.mp3",
            ]:
                full_path = os.path.join(output_directory, path)
                if os.path.exists(full_path):
                    os.remove(full_path)

    def run_postprocessing(self) -> None:
        """merges dubbed audio with the original background audio and video (if applicable).

        returns:
            path to the final dubbed output file.
        """
        dubbed_audio_vocals_file = audio_processing.insert_audio_at_timestamps(
            utterance_metadata=self.utterance_metadata,
            background_audio_file=self.preprocessing_output.audio_background_file,
            output_directory=self.output_directory,
        )
        # prepare audio configuration and vocal segments for smart processing
        audio_config = None
        vocal_segments = None

        if self.run_config and hasattr(self.run_config, "audio_processing"):
            audio_config = self.run_config.audio_processing

            # extract vocal segments from utterance metadata for background ducking
            vocal_segments = []
            for utterance in self.utterance_metadata:
                if utterance.get(
                    "for_dubbing", True
                ):  # only include segments that were dubbed
                    vocal_segments.append(
                        {
                            "start": utterance.get("start", 0),
                            "end": utterance.get("end", 0),
                        }
                    )

        dubbed_audio_file = audio_processing.merge_background_and_vocals(
            background_audio_file=self.preprocessing_output.audio_background_file,
            dubbed_vocals_audio_file=dubbed_audio_vocals_file,
            output_directory=self.output_directory,
            target_language=self.target_language,
            vocals_volume_adjustment=5.0,  # will be overridden by smart processing if enabled
            background_volume_adjustment=0.0,
            audio_config=audio_config,
            vocal_segments=vocal_segments,
        )
        if not self.preprocessing_output.video_file:
            raise ValueError(
                "a video file must be provided if the input file is a video."
            )
        dubbed_video_file = VideoProcessing.combine_audio_video(
            video_file=self.preprocessing_output.video_file,
            dubbed_audio_file=dubbed_audio_file,
            output_directory=self.output_directory,
            target_language=self.target_language,
        )
        self.postprocessing_output = PostprocessingArtifacts(
            audio_file=dubbed_audio_file,
            video_file=dubbed_video_file,
        )

    def _save_utterances(self):
        """saves the utterance metadata and preprocessing output."""
        metadata = {
            "source_language": self.source_language,
            "original_subtitles": self.original_subtitles_filename,
            "dubbed_subtitles": self.original_dubbed_subtitles_filename,
        }
        Utterance(self.target_language, self.output_directory).save_utterances(
            utterance_metadata=self.utterance_metadata,
            preprocessing_output=self.preprocessing_output,
            metadata=metadata,
        )

    def update(self):
        """updates the dubbing process based on existing metadata."""
        times = {}
        start_time = time.time()

        logger().info("update dubbing process started")

        try:
            utterance = Utterance(self.target_language, self.output_directory)
            self.utterance_metadata, self.preprocessing_output, _ = (
                utterance.load_utterances()
            )
        except Exception as e:
            logger().error(
                f"unable to read metadata at '{self.output_directory}. "
                f"cannot find a previous execution to update. error: '{e}'"
            )
            exit(ExitCode.update_missing_files)

        _, dubbed_paths = utterance.get_files_paths(self.utterance_metadata)
        for path in dubbed_paths:
            if not os.path.exists(path):
                logger().error(
                    f"cannot do update operation since file '{path}' is missing."
                )
                exit(ExitCode.update_missing_files)

        # update voices in case voices, text or time has changed
        modified_utterances = utterance.get_modified_utterances(self.utterance_metadata)

        assigned_voices = self.tts.assign_voices(
            utterance_metadata=modified_utterances,
            target_language=self.target_language,
            target_language_region=self.target_language_region,
        )

        modified_utterances = self.tts.update_utterance_metadata(
            utterance=utterance,
            utterance_metadata=modified_utterances,
            assigned_voices=assigned_voices,
        )

        self.utterance_metadata = self.tts.dub_utterances(
            utterance_metadata=self.utterance_metadata,
            output_directory=self.output_directory,
            target_language=self.target_language,
            audio_file=self.preprocessing_output.audio_file,
            modified_metadata=modified_utterances,
        )
        times["tts"] = self.log_debug_task_and_getime(
            "text to speech completed", start_time
        )

        task_start_time = time.time()
        self.run_postprocessing()
        self.run_generate_subtitles()
        self._save_utterances()
        times["postprocessing"] = self.log_debug_task_and_getime(
            "post processing completed", task_start_time
        )
        logger().info("dubbing process finished.")
        total_time = time.time() - start_time
        logger().info(f"total execution time: {total_time:.2f} secs")
        for task in times:
            _time = times[task]
            per = _time * 100 / total_time
            logger().info(f" task '{task}' in {_time:.2f} secs ({per:.2f}%)")

        self.log_maxrss_memory()
        logger().info("output files saved in: %s.", self.output_directory)

    def run_generate_subtitles(self):
        """generates subtitles for the original and dubbed languages."""
        if (
            not self.original_subtitles_filename
            and not self.original_dubbed_subtitles_filename
        ):
            return

        subtitles = Subtitles()

        filename = f"{self.source_language}.srt"
        source_srt = subtitles.write(
            utterance_metadata=self.utterance_metadata,
            directory=self.output_directory,
            filename=filename,
            translated=False,
        )

        filename = f"{self.target_language}.srt"
        target_srt = subtitles.write(
            utterance_metadata=self.utterance_metadata,
            directory=self.output_directory,
            filename=filename,
            translated=True,
        )

        subtitles_files = []
        languages_iso_639_3 = []

        if self.original_subtitles_filename:
            subtitles_files.append(source_srt)
            languages_iso_639_3.append(self.source_language)

        if self.original_dubbed_subtitles_filename:
            subtitles_files.append(target_srt)
            languages_iso_639_3.append(self.target_language)

        FFmpeg(FFmpeg.get_recommended_hwaccel()).embed_subtitles(
            video_file=self.postprocessing_output.video_file,
            subtitles_files=subtitles_files,
            languages_iso_639_3=languages_iso_639_3,
        )
        logger().info(f"generated subtitles for languages {languages_iso_639_3}")

    def dub(self) -> PostprocessingArtifacts:
        """orchestrates the entire dubbing process."""
        try:
            self._verify_api_access()
            logger().info("dubbing process starting...")
            times = {}
            start_time = time.time()

            task_start_time = time.time()
            self.run_preprocessing()
            times["preprocessing"] = self.log_debug_task_and_getime(
                "preprocessing completed", task_start_time
            )
            task_start_time = time.time()
            self.run_speech_to_text()
            times["stt"] = self.log_debug_task_and_getime(
                "speech to text completed", task_start_time
            )
            task_start_time = time.time()

            self.run_translation()
            times["translation"] = self.log_debug_task_and_getime(
                "translation completed", task_start_time
            )

            task_start_time = time.time()
            self.run_configure_text_to_speech()
            self.run_text_to_speech()
            times["tts"] = self.log_debug_task_and_getime(
                "text to speech completed", task_start_time
            )

            task_start_time = time.time()

            self.run_postprocessing()
            self.run_generate_subtitles()
            self._save_utterances()
            self.run_cleaning()
            times["postprocessing"] = self.log_debug_task_and_getime(
                "post processing completed", task_start_time
            )
            logger().info("dubbing process finished.")
            total_time = time.time() - start_time
            logger().info(f"total execution time: {total_time:.2f} secs")
            for task in times:
                _time = times[task]
                per = _time * 100 / total_time
                logger().info(f" task '{task}' in {_time:.2f} secs ({per:.2f}%)")

            self.log_maxrss_memory()
            if logger().getEffectiveLevel() == logging.DEBUG:
                self.stt.dump_transcriptions(
                    output_directory=self.output_directory,
                    utterance_metadata=self.utterance_metadata,
                )

            logger().info("output files saved in: %s.", self.output_directory)

            # update run status to completed
            self._update_run_status("completed")

            return self.postprocessing_output
        except Exception as e:
            # update run status to failed
            self._update_run_status("failed")
            logger().error(f"dubbing process failed: {e}")
            raise

    async def dub_async(self) -> PostprocessingArtifacts:
        """
        async version of the dubbing process with optimized speech-to-text.

        provides massive performance improvements by using parallel transcription,
        reducing overall dubbing time by 2-5x depending on video length.
        """
        try:
            self._verify_api_access()
            logger().info("async dubbing process starting...")
            times = {}
            start_time = time.time()

            task_start_time = time.time()
            self.run_preprocessing()
            times["preprocessing"] = self.log_debug_task_and_getime(
                "preprocessing completed", task_start_time
            )

            task_start_time = time.time()
            await self.run_speech_to_text_async()  # async transcription
            times["stt"] = self.log_debug_task_and_getime(
                "async speech to text completed", task_start_time
            )

            task_start_time = time.time()
            self.run_translation()
            times["translation"] = self.log_debug_task_and_getime(
                "translation completed", task_start_time
            )

            task_start_time = time.time()
            self.run_configure_text_to_speech()
            self.run_text_to_speech()
            times["tts"] = self.log_debug_task_and_getime(
                "text to speech completed", task_start_time
            )

            task_start_time = time.time()
            self.run_postprocessing()
            self.run_generate_subtitles()
            self._save_utterances()
            self.run_cleaning()
            times["postprocessing"] = self.log_debug_task_and_getime(
                "post processing completed", task_start_time
            )

            logger().info("async dubbing process finished.")
            total_time = time.time() - start_time
            logger().info(f"total execution time: {total_time:.2f} secs")
            for task in times:
                _time = times[task]
                per = _time * 100 / total_time
                logger().info(f" task '{task}' in {_time:.2f} secs ({per:.2f}%)")

            self.log_maxrss_memory()
            if logger().getEffectiveLevel() == logging.DEBUG:
                self.stt.dump_transcriptions(
                    output_directory=self.output_directory,
                    utterance_metadata=self.utterance_metadata,
                )

            logger().info("output files saved in: %s.", self.output_directory)

            # update run status to completed
            self._update_run_status("completed")

            return self.postprocessing_output
        except Exception as e:
            # update run status to failed
            self._update_run_status("failed")
            logger().error(f"async dubbing process failed: {e}")
            raise

    def cleanup(self):
        """
        clean up resources and save current progress during graceful shutdown.

        this method ensures that any in-progress work is saved and resources
        are properly released when the application is being shutdown.
        """
        logger().info("cleaning up resources during shutdown...")

        try:
            # save any in-progress utterances
            if hasattr(self, "utterance_metadata") and self.utterance_metadata:
                logger().info("saving utterance metadata...")
                self._save_utterances()

            # close any open file handles (if applicable)

            # release hardware resources (if applicable)
            if hasattr(self, "pyannote_pipeline") and self.pyannote_pipeline:
                logger().info("releasing pyannote pipeline resources...")
                # the pipeline might not have a direct close/release method
                # but we can remove the reference to help with garbage collection
                self.pyannote_pipeline = None

            logger().info("cleanup completed successfully")
        except Exception as e:
            logger().error(f"error during cleanup: {e}")
            # continue with cleanup even if parts fail
