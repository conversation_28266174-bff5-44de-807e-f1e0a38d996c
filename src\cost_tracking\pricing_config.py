"""pricing configuration management for ai providers.

this module provides default pricing configurations for all supported ai providers
and allows for custom pricing configuration loading from files.
"""

import json
from decimal import Decimal
from pathlib import Path
from typing import Dict, Optional

from src.utils.logger import logger
from .models import PricingConfig, ProviderType, PricingModel


class PricingConfigManager:
    """manages pricing configurations for all ai providers."""

    def __init__(self, config_file: Optional[Path] = None):
        self.config_file = config_file or Path("data/pricing_config.json")
        self._pricing_configs: Dict[ProviderType, PricingConfig] = {}
        self._load_default_configs()
        self._load_custom_configs()

    def get_pricing_config(self, provider: ProviderType) -> PricingConfig:
        """get pricing configuration for a provider."""
        return self._pricing_configs.get(provider, self._get_default_config(provider))

    def update_pricing_config(
        self, provider: ProviderType, config: PricingConfig
    ) -> None:
        """update pricing configuration for a provider."""
        self._pricing_configs[provider] = config
        self._save_custom_configs()
        logger().info(f"updated pricing config for {provider.value}")

    def _load_default_configs(self) -> None:
        """load default pricing configurations."""
        # assemblyai pricing (as of 2024)
        self._pricing_configs[ProviderType.ASSEMBLYAI] = PricingConfig(
            provider=ProviderType.ASSEMBLYAI,
            pricing_model=PricingModel.PER_MINUTE,
            base_cost=Decimal("0.37"),  # $0.37 per minute for best model
            tier_multipliers={
                "best": Decimal("1.0"),
                "nano": Decimal("0.27"),  # $0.10 per minute
                "universal": Decimal("0.54"),  # $0.20 per minute
            },
            metadata={
                "currency": "USD",
                "features": {
                    "speaker_labels": Decimal("0.10"),  # additional cost
                    "sentiment_analysis": Decimal("0.05"),
                    "entity_detection": Decimal("0.05"),
                    "auto_highlights": Decimal("0.03"),
                },
            },
        )

        # gemini pricing (as of 2024)
        self._pricing_configs[ProviderType.GEMINI] = PricingConfig(
            provider=ProviderType.GEMINI,
            pricing_model=PricingModel.PER_TOKEN,
            base_cost=Decimal("0.000125"),  # $0.125 per 1k input tokens
            tier_multipliers={
                "gemini-pro": Decimal("1.0"),
                "gemini-pro-vision": Decimal("1.5"),
                "gemini-ultra": Decimal("3.0"),
            },
            metadata={
                "currency": "USD",
                "output_token_multiplier": Decimal("2.5"),  # output tokens cost more
                "rate_limit_rpm": 1000,  # requests per minute
            },
        )

        # azure tts pricing
        self._pricing_configs[ProviderType.AZURE_TTS] = PricingConfig(
            provider=ProviderType.AZURE_TTS,
            pricing_model=PricingModel.PER_CHARACTER,
            base_cost=Decimal("0.000016"),  # $16 per 1m characters
            tier_multipliers={
                "standard": Decimal("1.0"),
                "neural": Decimal("2.0"),  # $32 per 1m characters
                "premium": Decimal("3.0"),
            },
            metadata={
                "currency": "USD",
                "quality_multipliers": {
                    "standard": Decimal("1.0"),
                    "high": Decimal("1.2"),
                    "maximum": Decimal("1.5"),
                },
            },
        )

        # elevenlabs tts pricing
        self._pricing_configs[ProviderType.ELEVENLABS_TTS] = PricingConfig(
            provider=ProviderType.ELEVENLABS_TTS,
            pricing_model=PricingModel.PER_CHARACTER,
            base_cost=Decimal("0.00003"),  # $30 per 1m characters
            tier_multipliers={
                "standard": Decimal("1.0"),
                "premium": Decimal("2.0"),
                "custom": Decimal("5.0"),
            },
            volume_discounts={
                1000000: Decimal("0.1"),  # 10% discount for 1m+ characters
                5000000: Decimal("0.2"),  # 20% discount for 5m+ characters
            },
            metadata={
                "currency": "USD",
                "voice_cloning_cost": Decimal("11.0"),  # $11 per voice clone
            },
        )

        # yandex tts pricing
        self._pricing_configs[ProviderType.YANDEX_TTS] = PricingConfig(
            provider=ProviderType.YANDEX_TTS,
            pricing_model=PricingModel.PER_CHARACTER,
            base_cost=Decimal("0.000015"),  # $15 per 1m characters
            tier_multipliers={
                "standard": Decimal("1.0"),
                "premium": Decimal("1.5"),
            },
            metadata={
                "currency": "USD",
                "language_multipliers": {
                    "ru": Decimal("1.0"),
                    "en": Decimal("1.0"),
                    "other": Decimal("1.2"),
                },
            },
        )

        # gpu processing pricing (estimated cloud gpu costs)
        self._pricing_configs[ProviderType.GPU_PROCESSING] = PricingConfig(
            provider=ProviderType.GPU_PROCESSING,
            pricing_model=PricingModel.PER_SECOND,
            base_cost=Decimal("0.0028"),  # ~$10/hour for high-end gpu
            tier_multipliers={
                "demucs": Decimal("1.5"),  # audio separation is intensive
                "diarization": Decimal("1.2"),
                "video_processing": Decimal("1.0"),
            },
            metadata={
                "currency": "USD",
                "device_types": {
                    "rtx_4090": Decimal("1.0"),
                    "a100": Decimal("2.5"),
                    "h100": Decimal("4.0"),
                },
            },
        )

        # cpu processing pricing
        self._pricing_configs[ProviderType.CPU_PROCESSING] = PricingConfig(
            provider=ProviderType.CPU_PROCESSING,
            pricing_model=PricingModel.PER_SECOND,
            base_cost=Decimal("0.0008"),  # ~$3/hour for high-end cpu
            tier_multipliers={
                "audio_processing": Decimal("0.8"),
                "video_processing": Decimal("1.0"),
                "general": Decimal("0.6"),
            },
            metadata={
                "currency": "USD",
                "cpu_types": {
                    "intel_i9": Decimal("1.0"),
                    "amd_ryzen": Decimal("0.9"),
                    "cloud_cpu": Decimal("1.2"),
                },
            },
        )

    def _load_custom_configs(self) -> None:
        """load custom pricing configurations from file."""
        if not self.config_file.exists():
            logger().debug("no custom pricing config file found, using defaults")
            return

        try:
            with open(self.config_file, "r") as f:
                data = json.load(f)

            for provider_name, config_data in data.items():
                try:
                    provider = ProviderType(provider_name)

                    # convert decimal fields
                    config_data["base_cost"] = Decimal(str(config_data["base_cost"]))
                    if "tier_multipliers" in config_data:
                        config_data["tier_multipliers"] = {
                            k: Decimal(str(v))
                            for k, v in config_data["tier_multipliers"].items()
                        }
                    if "volume_discounts" in config_data:
                        config_data["volume_discounts"] = {
                            int(k): Decimal(str(v))
                            for k, v in config_data["volume_discounts"].items()
                        }

                    config = PricingConfig(**config_data)
                    self._pricing_configs[provider] = config
                    logger().debug(f"loaded custom pricing config for {provider.value}")

                except Exception as e:
                    logger().warning(
                        f"failed to load pricing config for {provider_name}: {e}"
                    )

        except Exception as e:
            logger().warning(f"failed to load custom pricing configs: {e}")

    def _save_custom_configs(self) -> None:
        """save custom pricing configurations to file."""
        try:
            self.config_file.parent.mkdir(parents=True, exist_ok=True)

            data = {}
            for provider, config in self._pricing_configs.items():
                config_dict = config.dict()

                # convert decimal fields to strings for json serialization
                config_dict["base_cost"] = str(config_dict["base_cost"])
                if "tier_multipliers" in config_dict:
                    config_dict["tier_multipliers"] = {
                        k: str(v) for k, v in config_dict["tier_multipliers"].items()
                    }
                if "volume_discounts" in config_dict:
                    config_dict["volume_discounts"] = {
                        str(k): str(v)
                        for k, v in config_dict["volume_discounts"].items()
                    }

                data[provider.value] = config_dict

            with open(self.config_file, "w") as f:
                json.dump(data, f, indent=2, default=str)

            logger().debug("saved custom pricing configurations")

        except Exception as e:
            logger().error(f"failed to save custom pricing configs: {e}")

    def _get_default_config(self, provider: ProviderType) -> PricingConfig:
        """get default configuration for a provider."""
        return PricingConfig(
            provider=provider,
            pricing_model=PricingModel.PER_REQUEST,
            base_cost=Decimal("0.01"),  # fallback cost
        )

    def export_pricing_template(self, output_file: Path) -> None:
        """export a template pricing configuration file."""
        template = {}
        for provider, config in self._pricing_configs.items():
            config_dict = config.dict()
            # convert decimals to strings for template
            config_dict["base_cost"] = str(config_dict["base_cost"])
            if "tier_multipliers" in config_dict:
                config_dict["tier_multipliers"] = {
                    k: str(v) for k, v in config_dict["tier_multipliers"].items()
                }
            if "volume_discounts" in config_dict:
                config_dict["volume_discounts"] = {
                    str(k): str(v) for k, v in config_dict["volume_discounts"].items()
                }
            template[provider.value] = config_dict

        with open(output_file, "w") as f:
            json.dump(template, f, indent=2, default=str)

        logger().info(f"exported pricing template to {output_file}")


# global pricing config manager instance
_pricing_manager: Optional[PricingConfigManager] = None


def get_pricing_manager() -> PricingConfigManager:
    """get global pricing configuration manager."""
    global _pricing_manager
    if _pricing_manager is None:
        _pricing_manager = PricingConfigManager()
    return _pricing_manager


def get_pricing_config(provider: ProviderType) -> PricingConfig:
    """get pricing configuration for a provider."""
    return get_pricing_manager().get_pricing_config(provider)
