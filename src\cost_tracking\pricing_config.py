"""pricing configuration management for ai providers.

this module provides default pricing configurations for all supported ai providers
and allows for custom pricing configuration loading from files.
"""

import json
from decimal import Decimal
from pathlib import Path
from typing import Dict, Optional

from src.utils.logger import logger
from .models import PricingConfig, ProviderType, PricingModel


class PricingConfigManager:
    """manages pricing configurations for all ai providers."""

    def __init__(self, config_file: Optional[Path] = None):
        self.config_file = config_file or Path("data/pricing_config.json")
        self.default_config_file = Path("data/default_pricing_config.json")
        self._pricing_configs: Dict[ProviderType, PricingConfig] = {}
        self._load_default_configs()
        self._load_custom_configs()

    def get_pricing_config(self, provider: ProviderType) -> PricingConfig:
        """get pricing configuration for a provider."""
        return self._pricing_configs.get(provider, self._get_default_config(provider))

    def update_pricing_config(
        self, provider: ProviderType, config: PricingConfig
    ) -> None:
        """update pricing configuration for a provider."""
        self._pricing_configs[provider] = config
        self._save_custom_configs()
        logger().info(f"updated pricing config for {provider.value}")

    def _load_default_configs(self) -> None:
        """load default pricing configurations from json file."""
        try:
            if self.default_config_file.exists():
                logger().debug(
                    f"loading default pricing config from {self.default_config_file}"
                )
                self._load_configs_from_file(self.default_config_file)
            else:
                logger().warning(
                    f"default pricing config file not found: {self.default_config_file}"
                )
                self._load_hardcoded_fallback_configs()
        except Exception as e:
            logger().error(f"failed to load default pricing configs: {e}")
            self._load_hardcoded_fallback_configs()

    def _load_configs_from_file(self, config_file: Path) -> None:
        """load pricing configurations from a json file."""
        with open(config_file, "r") as f:
            data = json.load(f)

        for provider_name, config_data in data.items():
            try:
                provider = ProviderType(provider_name)

                # convert decimal fields
                config_data["base_cost"] = Decimal(str(config_data["base_cost"]))
                if "tier_multipliers" in config_data:
                    config_data["tier_multipliers"] = {
                        k: Decimal(str(v))
                        for k, v in config_data["tier_multipliers"].items()
                    }
                if "volume_discounts" in config_data:
                    config_data["volume_discounts"] = {
                        int(k): Decimal(str(v))
                        for k, v in config_data["volume_discounts"].items()
                    }

                config = PricingConfig(**config_data)
                self._pricing_configs[provider] = config
                logger().debug(f"loaded pricing config for {provider.value}")

            except Exception as e:
                logger().warning(
                    f"failed to load pricing config for {provider_name}: {e}"
                )

    def _load_hardcoded_fallback_configs(self) -> None:
        """load minimal hardcoded fallback configurations."""
        logger().warning("using hardcoded fallback pricing configurations")

        # minimal fallback configs
        fallback_configs = {
            ProviderType.ASSEMBLYAI: PricingConfig(
                provider=ProviderType.ASSEMBLYAI,
                pricing_model=PricingModel.PER_MINUTE,
                base_cost=Decimal("0.37"),
                tier_multipliers={"best": Decimal("1.0"), "nano": Decimal("0.27")},
                metadata={"currency": "USD"},
            ),
            ProviderType.GEMINI: PricingConfig(
                provider=ProviderType.GEMINI,
                pricing_model=PricingModel.PER_TOKEN,
                base_cost=Decimal("0.000125"),
                tier_multipliers={"gemini-pro": Decimal("1.0")},
                metadata={"currency": "USD"},
            ),
            ProviderType.AZURE_TTS: PricingConfig(
                provider=ProviderType.AZURE_TTS,
                pricing_model=PricingModel.PER_CHARACTER,
                base_cost=Decimal("0.000016"),
                tier_multipliers={"neural": Decimal("1.0")},
                metadata={"currency": "USD"},
            ),
        }

        for provider, config in fallback_configs.items():
            self._pricing_configs[provider] = config

    def _load_custom_configs(self) -> None:
        """load custom pricing configurations from file."""
        if not self.config_file.exists():
            logger().debug("no custom pricing config file found, using defaults")
            return

        try:
            logger().debug(f"loading custom pricing config from {self.config_file}")
            self._load_configs_from_file(self.config_file)
        except Exception as e:
            logger().warning(f"failed to load custom pricing configs: {e}")

    def _save_custom_configs(self) -> None:
        """save custom pricing configurations to file."""
        try:
            self.config_file.parent.mkdir(parents=True, exist_ok=True)

            data = {}
            for provider, config in self._pricing_configs.items():
                config_dict = config.dict()

                # convert decimal fields to strings for json serialization
                config_dict["base_cost"] = str(config_dict["base_cost"])
                if "tier_multipliers" in config_dict:
                    config_dict["tier_multipliers"] = {
                        k: str(v) for k, v in config_dict["tier_multipliers"].items()
                    }
                if "volume_discounts" in config_dict:
                    config_dict["volume_discounts"] = {
                        str(k): str(v)
                        for k, v in config_dict["volume_discounts"].items()
                    }

                data[provider.value] = config_dict

            with open(self.config_file, "w") as f:
                json.dump(data, f, indent=2, default=str)

            logger().debug("saved custom pricing configurations")

        except Exception as e:
            logger().error(f"failed to save custom pricing configs: {e}")

    def _get_default_config(self, provider: ProviderType) -> PricingConfig:
        """get default configuration for a provider."""
        return PricingConfig(
            provider=provider,
            pricing_model=PricingModel.PER_REQUEST,
            base_cost=Decimal("0.01"),  # fallback cost
        )

    def export_pricing_template(self, output_file: Path) -> None:
        """export a template pricing configuration file."""
        template = {}
        for provider, config in self._pricing_configs.items():
            config_dict = config.dict()
            # convert decimals to strings for template
            config_dict["base_cost"] = str(config_dict["base_cost"])
            if "tier_multipliers" in config_dict:
                config_dict["tier_multipliers"] = {
                    k: str(v) for k, v in config_dict["tier_multipliers"].items()
                }
            if "volume_discounts" in config_dict:
                config_dict["volume_discounts"] = {
                    str(k): str(v) for k, v in config_dict["volume_discounts"].items()
                }
            template[provider.value] = config_dict

        with open(output_file, "w") as f:
            json.dump(template, f, indent=2, default=str)

        logger().info(f"exported pricing template to {output_file}")


# global pricing config manager instance
_pricing_manager: Optional[PricingConfigManager] = None


def get_pricing_manager() -> PricingConfigManager:
    """get global pricing configuration manager."""
    global _pricing_manager
    if _pricing_manager is None:
        _pricing_manager = PricingConfigManager()
    return _pricing_manager


def get_pricing_config(provider: ProviderType) -> PricingConfig:
    """get pricing configuration for a provider."""
    return get_pricing_manager().get_pricing_config(provider)
