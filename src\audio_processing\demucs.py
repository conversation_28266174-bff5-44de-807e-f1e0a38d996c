import os
import re
import subprocess
import sys
import time
from typing import Optional

from src.utils.logger import logger
from src.cost_tracking.processing_tracker import ProcessingTimeTracker


class Demucs:

    def build_demucs_command(
        self,
        *,
        audio_file: str,
        output_directory: str,
        device: str = "cuda",
        shifts: int = 10,  # increased from 1 to 10
        overlap: float = 0.25,
        jobs: int = 0,
        split: bool = False,  # disable splitting by default
        segment: int | None = None,
        model: str = "htdemucs_ft",  # new model parameter
    ) -> str:
        """builds the demucs audio separation command with quality optimizations.

        args:
            audio_file: the path to the audio file to process.
            output_directory: the output directory for separated tracks.
            device: the device to use ("cuda" or "cpu").
            shifts: the number of random shifts for equivariant stabilization.
            overlap: the overlap between splits.
            jobs: the number of jobs to run in parallel.
            split: whether to split audio into chunks (disabled for quality).
            segment: the split size for chunks (none for no splitting).
            mp3: convert output to mp3 (not recommended for quality).
            model: the separation model to use (htdemucs_ft for best quality).

        returns:
            a string representing the constructed command.
        """
        command_parts = [
            sys.executable,
            "-m",
            "demucs.separate",
            "-n",
            model,  # specify quality model
            "-o",
            f'"{output_directory}"',
            "--device",
            device,
            "--shifts",
            str(shifts),
            "--overlap",
            str(overlap),
            "-j",
            str(jobs),
            "--two-stems",
            "vocals",
        ]

        if not split:
            command_parts.append("--no-split")
        elif segment is not None:
            command_parts.extend(["--segment", str(segment)])

        command_parts.append(f'"{audio_file}"')
        return " ".join(command_parts)

    def execute_demucs_command(
        self, command: str, run_id: Optional[str] = None
    ) -> None:
        """executes demucs while streaming live progress to the logger.

        this implementation uses :pyclass:`subprocess.Popen` with a line-buffered
        pipe so that demucs progress bars (written to *stderr*) appear in real
        time in the console. it also records execution time for debugging and
        performance verification.
        """

        import time

        # log the exact command for transparency/debugging
        logger().info(f"executing demucs: {command}")

        # initialize cost tracking
        cost_tracker = ProcessingTimeTracker("demucs", "gpu")
        cost_tracker.start()

        start_ts = time.perf_counter()

        # open the subprocess with stdout/stderr merged so we capture progress
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,  # line-buffered
            universal_newlines=True,
        )

        # stream output line-by-line
        try:
            assert process.stdout is not None  # for type checkers
            for line in iter(process.stdout.readline, ""):
                # rstrip to remove line breaks for cleaner logs
                logger().info(line.rstrip())

            process.stdout.close()
            return_code = process.wait()

            duration = time.perf_counter() - start_ts

            if return_code != 0:
                raise RuntimeError(
                    f"demucs failed with exit code {return_code} after {duration:.2f}s"
                )

            logger().info(f"demucs separation completed in {duration:.2f}s")

            # track cost for successful completion
            cost_tracker.stop(run_id)

        except Exception as error:
            # track cost even on failure
            cost_tracker.stop(run_id)
            # ensure process is terminated to avoid zombies
            process.kill()
            process.wait()
            raise Exception(f"error while running demucs: {error}")

    def _extract_command_info(self, command: str) -> tuple[str, str, str]:
        """extracts output directory, file extension, and input filename."""
        folder_pattern = r"-o\s+(['\"]?)(.+?)\1"
        input_file_pattern = r"['\"]?(\w+\.\w+)['\"]?$|\s(\w+\.\w+)$"

        folder_match = re.search(folder_pattern, command)
        input_file_match = re.search(input_file_pattern, command)

        output_directory = folder_match.group(2) if folder_match else ""
        input_file_name_with_ext = (
            (input_file_match.group(1) or input_file_match.group(2))
            if input_file_match
            else ""
        )
        input_file_name_no_ext = (
            os.path.splitext(input_file_name_with_ext)[0] if input_file_match else ""
        )

        # wav is default output format now
        return output_directory, ".wav", input_file_name_no_ext

    def assemble_split_audio_file_paths(self, command: str) -> tuple[str, str]:
        """returns paths to vocals and background audio files."""
        # attempt to parse model name from command (after '-n')
        model_match = re.search(r"-n\s+([\w\-]+)", command)
        model_name = model_match.group(1) if model_match else "htdemucs_ft"

        output_dir, ext, input_name = self._extract_command_info(command)

        vocals_path = os.path.join(output_dir, model_name, input_name, f"vocals{ext}")
        background_path = os.path.join(
            output_dir, model_name, input_name, f"no_vocals{ext}"
        )
        return vocals_path, background_path
