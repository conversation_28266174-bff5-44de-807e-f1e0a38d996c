{"basic_budget": {"description": "basic budget configuration for small projects", "daily_limit": "25.00", "weekly_limit": "150.00", "monthly_limit": "500.00", "per_job_limit": "10.00", "alert_thresholds": [0.8, 0.9, 1.0], "enforcement_enabled": true, "notification_emails": ["<EMAIL>"]}, "development_budget": {"description": "budget configuration for development and testing", "daily_limit": "10.00", "weekly_limit": "50.00", "monthly_limit": "200.00", "per_job_limit": "5.00", "alert_thresholds": [0.7, 0.85, 1.0], "enforcement_enabled": false, "notification_emails": ["<EMAIL>"]}, "production_budget": {"description": "production budget with strict controls", "daily_limit": "200.00", "weekly_limit": "1200.00", "monthly_limit": "4000.00", "per_job_limit": "100.00", "alert_thresholds": [0.75, 0.85, 0.95, 1.0], "enforcement_enabled": true, "notification_emails": ["<EMAIL>", "<EMAIL>", "<EMAIL>"]}, "enterprise_budget": {"description": "enterprise budget for large-scale operations", "daily_limit": "1000.00", "weekly_limit": "6000.00", "monthly_limit": "20000.00", "per_job_limit": "500.00", "alert_thresholds": [0.8, 0.9, 0.95, 1.0], "enforcement_enabled": true, "notification_emails": ["<EMAIL>", "<EMAIL>", "<EMAIL>"]}, "startup_budget": {"description": "budget configuration for startups with limited resources", "daily_limit": "15.00", "weekly_limit": "75.00", "monthly_limit": "300.00", "per_job_limit": "8.00", "alert_thresholds": [0.6, 0.8, 0.9, 1.0], "enforcement_enabled": true, "notification_emails": ["<EMAIL>"]}, "agency_budget": {"description": "budget configuration for agencies handling multiple clients", "daily_limit": "500.00", "weekly_limit": "3000.00", "monthly_limit": "10000.00", "per_job_limit": "200.00", "alert_thresholds": [0.8, 0.9, 1.0], "enforcement_enabled": true, "notification_emails": ["<EMAIL>", "<EMAIL>"]}, "freelancer_budget": {"description": "budget configuration for freelancers", "daily_limit": "30.00", "weekly_limit": "180.00", "monthly_limit": "600.00", "per_job_limit": "25.00", "alert_thresholds": [0.8, 0.9, 1.0], "enforcement_enabled": true, "notification_emails": ["<EMAIL>"]}, "educational_budget": {"description": "budget configuration for educational institutions", "daily_limit": "50.00", "weekly_limit": "250.00", "monthly_limit": "1000.00", "per_job_limit": "20.00", "alert_thresholds": [0.7, 0.85, 0.95, 1.0], "enforcement_enabled": true, "notification_emails": ["<EMAIL>", "<EMAIL>"]}, "research_budget": {"description": "budget configuration for research projects", "daily_limit": "100.00", "weekly_limit": "500.00", "monthly_limit": "2000.00", "per_job_limit": "75.00", "alert_thresholds": [0.8, 0.9, 1.0], "enforcement_enabled": false, "notification_emails": ["<EMAIL>", "<EMAIL>"]}, "media_company_budget": {"description": "budget configuration for media companies with high volume", "daily_limit": "800.00", "weekly_limit": "4800.00", "monthly_limit": "15000.00", "per_job_limit": "300.00", "alert_thresholds": [0.75, 0.85, 0.95, 1.0], "enforcement_enabled": true, "notification_emails": ["<EMAIL>", "<EMAIL>", "<EMAIL>"]}, "government_budget": {"description": "budget configuration for government agencies", "daily_limit": "300.00", "weekly_limit": "1500.00", "monthly_limit": "6000.00", "per_job_limit": "150.00", "alert_thresholds": [0.7, 0.8, 0.9, 0.95, 1.0], "enforcement_enabled": true, "notification_emails": ["<EMAIL>", "<EMAIL>", "<EMAIL>"]}, "nonprofit_budget": {"description": "budget configuration for nonprofit organizations", "daily_limit": "40.00", "weekly_limit": "200.00", "monthly_limit": "800.00", "per_job_limit": "30.00", "alert_thresholds": [0.6, 0.8, 0.9, 1.0], "enforcement_enabled": true, "notification_emails": ["<EMAIL>", "<EMAIL>"]}, "seasonal_budget": {"description": "budget configuration with seasonal variations", "q1_budget": {"daily_limit": "150.00", "monthly_limit": "4000.00"}, "q2_budget": {"daily_limit": "200.00", "monthly_limit": "5500.00"}, "q3_budget": {"daily_limit": "100.00", "monthly_limit": "2500.00"}, "q4_budget": {"daily_limit": "300.00", "monthly_limit": "8000.00"}, "alert_thresholds": [0.8, 0.9, 1.0], "enforcement_enabled": true, "notification_emails": ["<EMAIL>"]}, "project_based_budget": {"description": "budget configuration for project-based work", "project_alpha": {"total_budget": "5000.00", "daily_limit": "200.00", "per_job_limit": "100.00"}, "project_beta": {"total_budget": "2500.00", "daily_limit": "100.00", "per_job_limit": "50.00"}, "project_gamma": {"total_budget": "10000.00", "daily_limit": "400.00", "per_job_limit": "200.00"}, "alert_thresholds": [0.8, 0.9, 1.0], "enforcement_enabled": true, "notification_emails": ["<EMAIL>"]}, "client_specific_budget": {"description": "budget configuration with client-specific limits", "client_a": {"monthly_limit": "3000.00", "per_job_limit": "150.00"}, "client_b": {"monthly_limit": "1500.00", "per_job_limit": "75.00"}, "client_c": {"monthly_limit": "5000.00", "per_job_limit": "250.00"}, "default_client": {"monthly_limit": "1000.00", "per_job_limit": "50.00"}, "alert_thresholds": [0.8, 0.9, 1.0], "enforcement_enabled": true, "notification_emails": ["<EMAIL>"]}}