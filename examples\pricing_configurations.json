{"default_pricing": {"description": "default pricing configuration with current market rates", "assemblyai": {"provider": "assemblyai", "pricing_model": "per_minute", "base_cost": "0.37", "currency": "USD", "tier_multipliers": {"best": "1.0", "nano": "0.27", "universal": "0.54"}, "metadata": {"features": {"speaker_labels": "0.10", "sentiment_analysis": "0.05", "entity_detection": "0.05", "auto_highlights": "0.03"}}}, "gemini": {"provider": "gemini", "pricing_model": "per_token", "base_cost": "0.000125", "currency": "USD", "tier_multipliers": {"gemini-pro": "1.0", "gemini-ultra": "3.0", "gemini-flash": "0.4"}, "metadata": {"output_multiplier": "2.5"}}, "azure_tts": {"provider": "azure_tts", "pricing_model": "per_character", "base_cost": "0.000016", "currency": "USD", "tier_multipliers": {"standard": "1.0", "neural": "2.0", "custom": "4.0"}}, "elevenlabs_tts": {"provider": "elevenlabs_tts", "pricing_model": "per_character", "base_cost": "0.000030", "currency": "USD", "tier_multipliers": {"standard": "1.0", "premium": "1.5", "professional": "2.0"}}, "yandex_tts": {"provider": "yandex_tts", "pricing_model": "per_character", "base_cost": "0.000015", "currency": "USD", "tier_multipliers": {"standard": "1.0", "premium": "1.3"}}, "gpu_processing": {"provider": "gpu_processing", "pricing_model": "per_second", "base_cost": "0.0028", "currency": "USD", "tier_multipliers": {"demucs": "1.5", "diarization": "1.2", "video_processing": "1.0"}, "metadata": {"device_types": {"rtx_4090": "1.0", "a100": "2.5", "h100": "4.0"}}}, "cpu_processing": {"provider": "cpu_processing", "pricing_model": "per_second", "base_cost": "0.0008", "currency": "USD", "tier_multipliers": {"audio_processing": "0.8", "video_processing": "1.0", "general": "0.6"}}}, "cost_optimized_pricing": {"description": "cost-optimized pricing using cheaper models and providers", "assemblyai": {"provider": "assemblyai", "pricing_model": "per_minute", "base_cost": "0.10", "currency": "USD", "tier_multipliers": {"nano": "1.0", "universal": "2.0", "best": "3.7"}, "metadata": {"default_model": "nano", "features": {"speaker_labels": "0.05"}}}, "gemini": {"provider": "gemini", "pricing_model": "per_token", "base_cost": "0.00005", "currency": "USD", "tier_multipliers": {"gemini-flash": "1.0", "gemini-pro": "2.5", "gemini-ultra": "7.5"}, "metadata": {"default_model": "gemini-flash", "output_multiplier": "2.0"}}, "yandex_tts": {"provider": "yandex_tts", "pricing_model": "per_character", "base_cost": "0.000012", "currency": "USD", "tier_multipliers": {"standard": "1.0"}, "metadata": {"default_voice_type": "standard"}}, "gpu_processing": {"provider": "gpu_processing", "pricing_model": "per_second", "base_cost": "0.0020", "currency": "USD", "tier_multipliers": {"demucs": "1.2", "diarization": "1.0", "video_processing": "0.8"}}}, "premium_pricing": {"description": "premium pricing for highest quality models and services", "assemblyai": {"provider": "assemblyai", "pricing_model": "per_minute", "base_cost": "0.50", "currency": "USD", "tier_multipliers": {"best": "1.0", "universal": "0.6", "nano": "0.3"}, "metadata": {"default_model": "best", "features": {"speaker_labels": "0.15", "sentiment_analysis": "0.10", "entity_detection": "0.10", "auto_highlights": "0.05", "content_safety": "0.08"}}}, "gemini": {"provider": "gemini", "pricing_model": "per_token", "base_cost": "0.000375", "currency": "USD", "tier_multipliers": {"gemini-ultra": "1.0", "gemini-pro": "0.33", "gemini-flash": "0.13"}, "metadata": {"default_model": "gemini-ultra", "output_multiplier": "3.0"}}, "elevenlabs_tts": {"provider": "elevenlabs_tts", "pricing_model": "per_character", "base_cost": "0.000060", "currency": "USD", "tier_multipliers": {"professional": "1.0", "premium": "0.75", "standard": "0.5"}, "metadata": {"default_voice_type": "professional"}}, "gpu_processing": {"provider": "gpu_processing", "pricing_model": "per_second", "base_cost": "0.0056", "currency": "USD", "tier_multipliers": {"demucs": "2.0", "diarization": "1.5", "video_processing": "1.2"}, "metadata": {"device_types": {"h100": "1.0", "a100": "0.6", "rtx_4090": "0.25"}}}}, "volume_discount_pricing": {"description": "pricing with volume discounts for high-usage scenarios", "assemblyai": {"provider": "assemblyai", "pricing_model": "per_minute", "base_cost": "0.37", "currency": "USD", "tier_multipliers": {"best": "1.0", "nano": "0.27"}, "volume_discounts": {"100": "0.95", "500": "0.90", "1000": "0.85", "5000": "0.80"}, "metadata": {"volume_unit": "minutes_per_month"}}, "gemini": {"provider": "gemini", "pricing_model": "per_token", "base_cost": "0.000125", "currency": "USD", "tier_multipliers": {"gemini-pro": "1.0", "gemini-ultra": "3.0"}, "volume_discounts": {"1000000": "0.95", "5000000": "0.90", "10000000": "0.85", "50000000": "0.80"}, "metadata": {"volume_unit": "tokens_per_month"}}, "azure_tts": {"provider": "azure_tts", "pricing_model": "per_character", "base_cost": "0.000016", "currency": "USD", "tier_multipliers": {"neural": "1.0", "standard": "0.5"}, "volume_discounts": {"1000000": "0.95", "10000000": "0.90", "50000000": "0.85", "100000000": "0.80"}, "metadata": {"volume_unit": "characters_per_month"}}}, "enterprise_pricing": {"description": "enterprise pricing with custom rates and sla guarantees", "assemblyai": {"provider": "assemblyai", "pricing_model": "per_minute", "base_cost": "0.30", "currency": "USD", "tier_multipliers": {"best": "1.0", "universal": "0.5", "nano": "0.25"}, "volume_discounts": {"1000": "0.90", "10000": "0.80", "50000": "0.70"}, "metadata": {"sla_guarantee": "99.9%", "priority_support": true, "custom_models": true}}, "gemini": {"provider": "gemini", "pricing_model": "per_token", "base_cost": "0.0001", "currency": "USD", "tier_multipliers": {"gemini-pro": "1.0", "gemini-ultra": "2.5"}, "volume_discounts": {"10000000": "0.85", "100000000": "0.75", "500000000": "0.65"}, "metadata": {"dedicated_capacity": true, "custom_fine_tuning": true}}, "elevenlabs_tts": {"provider": "elevenlabs_tts", "pricing_model": "per_character", "base_cost": "0.000025", "currency": "USD", "tier_multipliers": {"professional": "1.0", "premium": "0.8", "standard": "0.6"}, "volume_discounts": {"50000000": "0.85", "200000000": "0.75", "**********": "0.65"}, "metadata": {"voice_cloning": true, "priority_processing": true}}}, "regional_pricing": {"description": "regional pricing variations for different markets", "us_pricing": {"assemblyai": {"base_cost": "0.37", "currency": "USD"}, "gemini": {"base_cost": "0.000125", "currency": "USD"}}, "eu_pricing": {"assemblyai": {"base_cost": "0.34", "currency": "EUR"}, "gemini": {"base_cost": "0.000115", "currency": "EUR"}}, "asia_pricing": {"assemblyai": {"base_cost": "0.32", "currency": "USD"}, "gemini": {"base_cost": "0.000110", "currency": "USD"}}}}