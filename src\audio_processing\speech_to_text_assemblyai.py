import array
import asyncio
import os
import re
import tempfile
import urllib.parse
import urllib.request
from typing import Mapping, Sequence, List, Dict, Optional
import time

import assemblyai as aai
from iso639 import Lang
from pydub import AudioSegment

from src.audio_processing.voice_gender_classifier import VoiceGenderClassifier
from src.utils.logger import logger
from src.cost_tracking.middleware import get_cost_tracker
from src.cost_tracking.models import ProviderType
from src.cost_tracking.calculators import AssemblyAICostCalculator
from src.cost_tracking.pricing_config import get_pricing_config


class SpeechToTextAssemblyAI:
    """assemblyai speech-to-text implementation with advanced features."""

    def __init__(
        self,
        *,
        model_name="best",  # assemblyai speech model (best, nano, slam-1, universal)
        device="cpu",  # assemblyai is cloud-based, device doesn't matter
        cpu_threads=0,  # not applicable for cloud service
        api_key: str | None = None,
        speaker_labels: bool = True,  # enable speaker diarization
        auto_highlights: bool = False,  # extract key phrases
        sentiment_analysis: bool = False,  # analyze sentiment
        entity_detection: bool = False,  # detect entities
        language_detection: bool = True,  # auto-detect language
    ):
        # initialize base attributes
        self.model_name = model_name
        self._model = None
        self.device = device
        self.cpu_threads = cpu_threads
        self.MIN_SECS = 0.5

        # get api key from parameter or environment
        self.api_key = api_key or os.environ.get("ASSEMBLYAI_API_KEY")
        if not self.api_key:
            logger().warning(
                "no assemblyai api key found. set assemblyai_api_key environment variable "
                "or pass api_key parameter. running in test mode."
            )
            self.api_key = "test_key"

        # configure assemblyai sdk
        if self.api_key != "test_key":
            aai.settings.api_key = self.api_key

        # assemblyai-specific features
        self.speaker_labels = speaker_labels
        self.auto_highlights = auto_highlights
        self.sentiment_analysis = sentiment_analysis
        self.entity_detection = entity_detection
        self.language_detection = language_detection

        # map model names to assemblyai speech models
        self.speech_model_map = {
            "best": aai.SpeechModel.best,
            "nano": aai.SpeechModel.nano,
            "slam-1": aai.SpeechModel.slam_1,
            "universal": aai.SpeechModel.universal,
        }

        # supported languages (assemblyai supports many languages)
        self._supported_languages = [
            "eng",
            "spa",
            "fra",
            "deu",
            "ita",
            "por",
            "rus",
            "jpn",
            "kor",
            "zho",
            "ara",
            "hin",
            "tur",
            "pol",
            "nld",
            "swe",
            "dan",
            "nor",
            "fin",
            "ces",
            "hun",
            "ron",
            "bul",
            "hrv",
            "slk",
            "slv",
            "est",
            "lav",
            "lit",
            "mlt",
            "ell",
            "heb",
            "tha",
            "vie",
            "ind",
            "msa",
            "tgl",
            "ukr",
            "bel",
            "kaz",
            "uzb",
            "aze",
            "kat",
            "hye",
            "fas",
            "urd",
            "ben",
            "guj",
            "pan",
            "tam",
            "tel",
            "kan",
            "mal",
            "ori",
            "mar",
            "nep",
            "sin",
            "mya",
            "khm",
            "lao",
        ]

        # initialize cost tracking
        self.cost_calculator = None
        self._initialize_cost_tracking()

        logger().info(
            f"initialized assemblyai stt with features: speaker_labels={speaker_labels}"
        )

    @property
    def model(self):
        return self._model

    @model.setter
    def model(self, value):
        self._model = value

    def _initialize_cost_tracking(self):
        """initialize cost tracking for assemblyai operations."""
        try:
            pricing_config = get_pricing_config(ProviderType.ASSEMBLYAI)
            self.cost_calculator = AssemblyAICostCalculator(pricing_config)
            logger().debug("initialized assemblyai cost tracking")
        except Exception as e:
            logger().warning(f"failed to initialize assemblyai cost tracking: {e}")
            self.cost_calculator = None

    def _track_transcription_cost(
        self, audio_duration_minutes: float, run_id: Optional[str] = None
    ):
        """track cost for a transcription operation."""
        if not self.cost_calculator or not run_id:
            return

        try:
            # get features used
            features = []
            if self.speaker_labels:
                features.append("speaker_labels")
            if self.sentiment_analysis:
                features.append("sentiment_analysis")
            if self.entity_detection:
                features.append("entity_detection")
            if self.auto_highlights:
                features.append("auto_highlights")

            # calculate cost
            cost_entry = self.cost_calculator.calculate_cost(
                audio_duration_minutes=audio_duration_minutes,
                model_name=self.model_name,
                features=features,
            )

            # add run_id to metadata
            cost_entry.metadata["run_id"] = run_id

            # track cost
            tracker = get_cost_tracker(run_id)
            if tracker:
                tracker.add_cost_entry(cost_entry)
                logger().debug(
                    f"tracked assemblyai cost: ${cost_entry.amount:.4f} "
                    f"for {audio_duration_minutes:.2f} minutes"
                )

        except Exception as e:
            logger().warning(f"failed to track assemblyai cost: {e}")

    def _get_iso_639_1(self, iso_639_3: str):
        o = Lang(iso_639_3)
        iso_639_1 = o.pt1
        return iso_639_1

    def _get_iso_639_3(self, iso_639_1: str):
        if iso_639_1 == "jw":
            iso_639_1 = "jv"

        o = Lang(iso_639_1)
        iso_639_3 = o.pt3
        return iso_639_3

    def _make_sure_single_space(self, sentence: str) -> str:
        """whisper sometimes includes spaces at the beginning of sentences or multiple spaces between words"""
        fixed = re.sub(r"\s{2,}", " ", sentence)
        fixed = fixed.strip()
        return fixed

    def _is_short_audio(self, duration: float) -> bool:
        return duration < self.MIN_SECS

    def load_model(self):
        """assemblyai is cloud-based, no local model loading required."""
        try:
            if self.api_key != "test_key":
                # verify api key by creating a transcriber instance
                self.transcriber = aai.Transcriber()
                logger().info("assemblyai api connection verified successfully")
            else:
                logger().warning(
                    "running assemblyai in test mode - api calls will be mocked"
                )
                self.transcriber = None

            self._model = f"assemblyai_{self.model_name}"

        except Exception as e:
            logger().error(f"failed to initialize assemblyai: {e}")
            raise

    def get_languages(self):
        """return list of supported languages in iso 639-3 format."""
        return self._supported_languages.copy()

    def detect_language(self, filename: str) -> str:
        """detect language from audio/video file, handling both local files and urls."""
        duration_secs = 30

        # handle urls by downloading them to a temporary file
        temp_file = None
        actual_filename = filename

        if filename.startswith(("http://", "https://", "ftp://", "ftps://")):
            logger().info(
                f"downloading video from url for language detection: {filename}"
            )

            # create a temporary file
            parsed_url = urllib.parse.urlparse(filename)
            file_ext = os.path.splitext(parsed_url.path)[1] or ".mp4"
            temp_file = tempfile.NamedTemporaryFile(suffix=file_ext, delete=False)
            temp_file.close()

            try:
                # download the file
                urllib.request.urlretrieve(filename, temp_file.name)
                actual_filename = temp_file.name
                logger().info(f"downloaded video to temporary file: {actual_filename}")
            except Exception as e:
                logger().error(f"failed to download video from url: {e}")
                if os.path.exists(temp_file.name):
                    os.unlink(temp_file.name)
                raise

        try:
            audio = AudioSegment.from_file(actual_filename)
            audio = audio.set_channels(1)
            audio = audio.set_frame_rate(16000)

            first_seconds = audio[: duration_secs * 1000].get_array_of_samples()
            return self._get_audio_language(first_seconds)
        finally:
            # clean up temporary file if it was created
            if temp_file and os.path.exists(temp_file.name):
                try:
                    os.unlink(temp_file.name)
                    logger().info(f"cleaned up temporary file: {temp_file.name}")
                except Exception as e:
                    logger().warning(f"failed to clean up temporary file: {e}")

    def _transcribe(
        self,
        *,
        vocals_filepath: str,
        source_language_iso_639_1: str,
    ) -> str:
        """transcribe audio file using assemblyai."""
        if self.api_key == "test_key":
            logger().warning("using test mode - returning placeholder transcription")
            return "this is a test transcription from assemblyai"

        try:
            # get the speech model to use
            speech_model = self.speech_model_map.get(
                self.model_name, aai.SpeechModel.best
            )

            # configure transcription settings based on assemblyai api
            config = aai.TranscriptionConfig(
                speech_model=speech_model,
                language_code=(
                    source_language_iso_639_1 if not self.language_detection else None
                ),
                language_detection=self.language_detection,
                speaker_labels=self.speaker_labels,
                auto_highlights=self.auto_highlights,
                sentiment_analysis=self.sentiment_analysis,
                entity_detection=self.entity_detection,
                punctuate=True,
                format_text=True,
            )

            transcriber = aai.Transcriber(config=config)

            logger().info(f"starting assemblyai transcription for: {vocals_filepath}")
            transcript = transcriber.transcribe(vocals_filepath)

            # the sdk handles polling automatically, but we can check status
            if transcript.status == aai.TranscriptStatus.error:
                logger().error(f"assemblyai transcription failed: {transcript.error}")
                return ""

            logger().info("assemblyai transcription completed successfully")

            # store additional data for potential future use
            if hasattr(transcript, "utterances") and transcript.utterances:
                logger().debug(
                    f"transcript contains {len(transcript.utterances)} utterances"
                )

            # ensure we have text before processing
            text = transcript.text or ""
            return self._make_sure_single_space(text)

        except Exception as e:
            logger().error(f"assemblyai transcription error: {e}")
            # fallback to empty string to prevent pipeline failure
            return ""

    def _get_audio_language(self, audio: array.array) -> str:
        """detect language from audio sample using assemblyai."""
        if self.api_key == "test_key":
            logger().warning("using test mode - returning default language")
            return "eng"

        try:
            # convert audio array to temporary file for assemblyai
            audio_segment = AudioSegment(
                audio.tobytes(), frame_rate=16000, sample_width=2, channels=1
            )

            # create temporary file
            temp_file = "temp_language_detection.wav"
            audio_segment.export(temp_file, format="wav")

            try:
                config = aai.TranscriptionConfig(
                    language_detection=True,
                    speaker_labels=False,
                )

                transcriber = aai.Transcriber(config=config)
                transcript = transcriber.transcribe(temp_file)

                if transcript.status == aai.TranscriptStatus.completed:
                    # assemblyai returns language detection in the transcript
                    detected_language = getattr(transcript, "language_code", None)
                    if detected_language:
                        # convert iso 639-1 to iso 639-3
                        iso_639_3 = self._get_iso_639_3(detected_language)
                        logger().debug(f"assemblyai detected language: {iso_639_3}")
                        return iso_639_3
                else:
                    logger().warning(
                        "assemblyai language detection failed, defaulting to english"
                    )
                    return "eng"

            finally:
                # cleanup temporary file
                if os.path.exists(temp_file):
                    os.remove(temp_file)

            # fallback if no language detected
            return "eng"

        except Exception as e:
            logger().error(f"assemblyai language detection error: {e}")
            return "eng"  # fallback to english

    def get_speaker_utterances(
        self, vocals_filepath: str, source_language_iso_639_1: str
    ):
        """get detailed utterance information with speaker labels from assemblyai."""
        if self.api_key == "test_key":
            logger().warning("speaker utterances not available in test mode")
            return []

        try:
            # get the speech model to use
            speech_model = self.speech_model_map.get(
                self.model_name, aai.SpeechModel.best
            )

            config = aai.TranscriptionConfig(
                speech_model=speech_model,
                language_code=(
                    source_language_iso_639_1 if not self.language_detection else None
                ),
                language_detection=self.language_detection,
                speaker_labels=True,  # force speaker labels for this method
                punctuate=True,
                format_text=True,
            )

            transcriber = aai.Transcriber(config=config)
            transcript = transcriber.transcribe(vocals_filepath)

            if (
                transcript.status == aai.TranscriptStatus.completed
                and transcript.utterances
            ):
                utterances = []
                for utterance in transcript.utterances:
                    utterances.append(
                        {
                            "speaker": utterance.speaker,
                            "text": self._make_sure_single_space(utterance.text),
                            "start": utterance.start / 1000.0,  # convert ms to seconds
                            "end": utterance.end / 1000.0,
                            "confidence": getattr(utterance, "confidence", 0.9),
                        }
                    )

                logger().info(f"extracted {len(utterances)} speaker utterances")
                return utterances
            else:
                logger().warning("no speaker utterances available from assemblyai")
                return []

        except Exception as e:
            logger().error(f"error getting speaker utterances: {e}")
            return []

    def transcribe_audio_chunks(
        self,
        *,
        utterance_metadata: Sequence[Mapping[str, float | str]],
        source_language: str,
        no_dubbing_phrases: Sequence[str],
        run_id: Optional[str] = None,
    ) -> Sequence[Mapping[str, float | str]]:
        logger().debug(f"transcribe_audio_chunks: {source_language}")
        iso_639_1 = self._get_iso_639_1(source_language)

        # calculate total audio duration for cost tracking
        total_duration_minutes = 0.0
        for item in utterance_metadata:
            duration = float(item["end"]) - float(item["start"])
            if not self._is_short_audio(duration=duration):
                total_duration_minutes += duration / 60.0

        updated_utterance_metadata = []
        for item in utterance_metadata:
            new_item = dict(item)  # convert to dict for copying
            text = ""
            try:
                path = str(item["path"])  # ensure path is string
                duration = float(item["end"]) - float(item["start"])  # ensure numeric

                if self._is_short_audio(duration=duration):
                    text = ""
                    logger().debug(
                        f"speech_to_text._is_short_audio. audio is less than {self.MIN_SECS} second, skipping transcription of '{path}'."
                    )
                else:
                    transcribed_text = self._transcribe(
                        vocals_filepath=path, source_language_iso_639_1=iso_639_1
                    )
                    text = self._make_sure_single_space(transcribed_text)

            except Exception as e:
                logger().error(
                    f"speech_to_text.transcribe_audio_chunks. file '{path}', error: '{e}'"
                )
                text = ""

            dubbing = len(text) > 0
            logger().debug(
                f"transcribe_audio_chunks. text: '{text}' - dubbing: {dubbing}"
            )
            new_item["text"] = text
            new_item["for_dubbing"] = dubbing
            updated_utterance_metadata.append(new_item)

        # track cost for all transcribed audio
        if total_duration_minutes > 0:
            self._track_transcription_cost(total_duration_minutes, run_id)

        return updated_utterance_metadata

    def _get_unique_speakers_largest_audio(self, utterance_metadata):
        speakers = {}
        for chunk in utterance_metadata:
            speaker_id = chunk["speaker_id"]
            length = chunk["end"] - chunk["start"]
            dubbed_path = chunk["path"]
            speaker_data = speakers.get(speaker_id, {})
            save = False
            if len(speaker_data) == 0:
                save = True
            elif length > speaker_data["length"]:
                save = True
            if save:
                speaker_data["length"] = length
                speaker_data["path"] = dubbed_path
                speakers[speaker_id] = speaker_data

        speaker_tuples = [(speaker, data["path"]) for speaker, data in speakers.items()]
        logger().debug(
            f"text_to_speech._get_unique_speakers_largest_audio: {speaker_tuples}"
        )
        return speaker_tuples

    def predict_gender(self, file, utterance_metadata):
        speaker_gender = {}
        classifier = VoiceGenderClassifier(self.device)
        speakers = self._get_unique_speakers_largest_audio(utterance_metadata)
        for speaker_id, path in speakers:
            gender = classifier.get_gender_for_file(path)
            speaker_gender[speaker_id] = gender

        r = []
        for chunk in utterance_metadata:
            speaker_id = chunk["speaker_id"]
            gender = speaker_gender[speaker_id]
            _tuple = (speaker_id, gender)
            r.append(_tuple)
        logger().debug(f"text_to_speech.diarize_speakers. returns: {r}".lower())
        return r

    def add_speaker_info(self, utterance_metadata, speaker_info):
        if len(utterance_metadata) != len(speaker_info):
            raise Exception(
                "the length of 'utterance_metadata' and 'speaker_info' must be the same."
            )

        updated_utterance_metadata = []
        for utterance, (speaker_id, gender) in zip(utterance_metadata, speaker_info):
            new_utterance = utterance.copy()
            new_utterance["speaker_id"] = speaker_id
            new_utterance["gender"] = gender
            updated_utterance_metadata.append(new_utterance)
        return updated_utterance_metadata

    def dump_transcriptions(
        self,
        *,
        output_directory: str,
        utterance_metadata: Sequence[Mapping[str, float | str]],
    ) -> None:
        output_filename = os.path.join(output_directory, "transcription.txt")

        with open(output_filename, "w") as _file:
            for utterance in utterance_metadata:
                text = str(utterance["text"])
                _file.write(text + "\n")

    # =========================================================================
    # async transcription methods for parallel processing
    # =========================================================================

    async def transcribe_audio_chunks_async(
        self,
        *,
        utterance_metadata: Sequence[Mapping[str, float | str]],
        source_language: str,
        no_dubbing_phrases: Sequence[str],
        max_concurrent: int = 10,
        progress_callback: Optional[callable] = None,
        run_id: Optional[str] = None,
    ) -> Sequence[Mapping[str, float | str]]:
        """
        transcribe multiple audio chunks asynchronously for massive speedup.

        args:
            utterance_metadata: list of audio chunks with metadata
            source_language: source language code
            no_dubbing_phrases: phrases to skip (unused but kept for compatibility)
            max_concurrent: maximum concurrent transcription jobs
            progress_callback: optional callback for progress updates

        returns:
            updated utterance metadata with transcribed text
        """
        logger().info(
            f"starting async transcription of {len(utterance_metadata)} chunks"
        )
        start_time = time.time()

        iso_639_1 = self._get_iso_639_1(source_language)

        # filter out short audio chunks
        chunks_to_process = []
        skipped_chunks = []

        for item in utterance_metadata:
            duration = float(item["end"]) - float(item["start"])
            if self._is_short_audio(duration=duration):
                skipped_chunks.append(item)
                logger().debug(
                    f"skipping short audio chunk: {item['path']} (duration: {duration:.2f}s)"
                )
            else:
                chunks_to_process.append(item)

        logger().info(
            f"processing {len(chunks_to_process)} chunks, skipping {len(skipped_chunks)} short chunks"
        )

        # process chunks in parallel
        if chunks_to_process:
            processed_chunks = await self._process_chunks_parallel(
                chunks_to_process=chunks_to_process,
                source_language_iso_639_1=iso_639_1,
                max_concurrent=max_concurrent,
                progress_callback=progress_callback,
            )
        else:
            processed_chunks = []

        # combine processed and skipped chunks
        all_chunks = []
        processed_idx = 0
        skipped_idx = 0

        for item in utterance_metadata:
            duration = float(item["end"]) - float(item["start"])
            if self._is_short_audio(duration=duration):
                # add skipped chunk with empty text
                new_item = dict(skipped_chunks[skipped_idx])
                new_item["text"] = ""
                new_item["for_dubbing"] = False
                all_chunks.append(new_item)
                skipped_idx += 1
            else:
                # add processed chunk
                all_chunks.append(processed_chunks[processed_idx])
                processed_idx += 1

        elapsed_time = time.time() - start_time
        logger().info(f"async transcription completed in {elapsed_time:.2f}s")

        # track cost for all transcribed audio
        if chunks_to_process and run_id:
            total_duration_minutes = sum(
                (float(chunk["end"]) - float(chunk["start"])) / 60.0
                for chunk in chunks_to_process
            )
            self._track_transcription_cost(total_duration_minutes, run_id)

        return all_chunks

    async def _process_chunks_parallel(
        self,
        *,
        chunks_to_process: List[Mapping[str, float | str]],
        source_language_iso_639_1: str,
        max_concurrent: int,
        progress_callback: Optional[callable] = None,
    ) -> List[Mapping[str, float | str]]:
        """
        process multiple audio chunks in parallel using assemblyai async api.

        this method implements intelligent batching and rate limiting to optimize
        performance while respecting api limits.
        """
        if self.api_key == "test_key":
            logger().warning("using test mode - returning placeholder transcriptions")
            return [
                {
                    **dict(chunk),
                    "text": f"test transcription for {chunk['path']}",
                    "for_dubbing": True,
                }
                for chunk in chunks_to_process
            ]

        # create transcription config
        speech_model = self.speech_model_map.get(self.model_name, aai.SpeechModel.best)
        config = aai.TranscriptionConfig(
            speech_model=speech_model,
            language_code=(
                source_language_iso_639_1 if not self.language_detection else None
            ),
            language_detection=self.language_detection,
            speaker_labels=self.speaker_labels,
            auto_highlights=self.auto_highlights,
            sentiment_analysis=self.sentiment_analysis,
            entity_detection=self.entity_detection,
            punctuate=True,
            format_text=True,
        )

        transcriber = aai.Transcriber(config=config)

        # submit all transcription jobs
        logger().info(f"submitting {len(chunks_to_process)} transcription jobs")
        submitted_jobs = []

        for chunk in chunks_to_process:
            try:
                path = str(chunk["path"])
                logger().debug(f"submitting transcription job for: {path}")

                # submit job (non-blocking)
                transcript = transcriber.submit(path)

                submitted_jobs.append(
                    {
                        "transcript_id": transcript.id,
                        "chunk_metadata": chunk,
                        "submitted_at": time.time(),
                    }
                )

                # small delay to avoid overwhelming the api
                await asyncio.sleep(0.1)

            except Exception as e:
                logger().error(
                    f"failed to submit transcription job for {chunk['path']}: {e}"
                )
                # add failed job with empty text
                submitted_jobs.append(
                    {
                        "transcript_id": None,
                        "chunk_metadata": chunk,
                        "submitted_at": time.time(),
                        "error": str(e),
                    }
                )

        logger().info(
            f"submitted {len([j for j in submitted_jobs if j['transcript_id']])} jobs successfully"
        )

        # poll for completion with intelligent batching
        completed_chunks = await self._poll_transcriptions_async(
            submitted_jobs=submitted_jobs,
            max_concurrent=max_concurrent,
            progress_callback=progress_callback,
        )

        return completed_chunks

    async def _poll_transcriptions_async(
        self,
        *,
        submitted_jobs: List[Dict],
        max_concurrent: int,
        progress_callback: Optional[callable] = None,
    ) -> List[Mapping[str, float | str]]:
        """
        poll multiple transcription jobs for completion using async approach.

        implements intelligent polling with exponential backoff and progress tracking.
        """
        logger().info(
            f"polling {len(submitted_jobs)} transcription jobs for completion"
        )

        # separate successful and failed submissions
        valid_jobs = [job for job in submitted_jobs if job.get("transcript_id")]
        failed_jobs = [job for job in submitted_jobs if not job.get("transcript_id")]

        if failed_jobs:
            logger().warning(f"{len(failed_jobs)} jobs failed to submit")

        # track completion status
        completed_results = []
        pending_jobs = valid_jobs.copy()
        poll_interval = 2.0  # start with 2 second intervals
        max_poll_interval = 10.0

        # add failed jobs as empty results
        for job in failed_jobs:
            chunk = dict(job["chunk_metadata"])
            chunk["text"] = ""
            chunk["for_dubbing"] = False
            completed_results.append(chunk)

        start_time = time.time()

        while pending_jobs:
            logger().debug(f"polling {len(pending_jobs)} pending jobs...")

            # check status of all pending jobs
            newly_completed = []
            still_pending = []

            for job in pending_jobs:
                try:
                    transcript_id = job["transcript_id"]
                    transcript = aai.Transcript.get_by_id(transcript_id)

                    if transcript.status == aai.TranscriptStatus.completed:
                        # job completed successfully
                        chunk = dict(job["chunk_metadata"])
                        text = transcript.text or ""
                        chunk["text"] = self._make_sure_single_space(text)
                        chunk["for_dubbing"] = len(text) > 0

                        completed_results.append(chunk)
                        newly_completed.append(job)

                        logger().debug(f"transcription completed: {chunk['path']}")

                    elif transcript.status == aai.TranscriptStatus.error:
                        # job failed
                        chunk = dict(job["chunk_metadata"])
                        chunk["text"] = ""
                        chunk["for_dubbing"] = False

                        completed_results.append(chunk)
                        newly_completed.append(job)

                        logger().error(
                            f"transcription failed: {chunk['path']} - {transcript.error}"
                        )

                    else:
                        # still processing
                        still_pending.append(job)

                except Exception as e:
                    logger().error(f"error checking transcript status: {e}")
                    # treat as failed
                    chunk = dict(job["chunk_metadata"])
                    chunk["text"] = ""
                    chunk["for_dubbing"] = False
                    completed_results.append(chunk)
                    newly_completed.append(job)

            # update progress
            total_jobs = len(submitted_jobs)
            completed_count = len(completed_results)

            if progress_callback:
                try:
                    progress_callback(completed_count, total_jobs)
                except Exception as e:
                    logger().warning(f"progress callback error: {e}")

            logger().info(
                f"transcription progress: {completed_count}/{total_jobs} completed"
            )

            # update pending jobs
            pending_jobs = still_pending

            if pending_jobs:
                # wait before next poll, with exponential backoff
                await asyncio.sleep(poll_interval)
                poll_interval = min(poll_interval * 1.2, max_poll_interval)

        elapsed_time = time.time() - start_time
        logger().info(f"all transcriptions completed in {elapsed_time:.2f}s")

        # sort results to match original order
        result_map = {chunk["path"]: chunk for chunk in completed_results}
        ordered_results = []

        for job in submitted_jobs:
            chunk_path = job["chunk_metadata"]["path"]
            if chunk_path in result_map:
                ordered_results.append(result_map[chunk_path])
            else:
                # fallback for missing results
                chunk = dict(job["chunk_metadata"])
                chunk["text"] = ""
                chunk["for_dubbing"] = False
                ordered_results.append(chunk)

        return ordered_results

    # =========================================================================
    # intelligent batching and rate limiting utilities
    # =========================================================================

    def _create_optimal_batches(
        self, chunks: List[Mapping[str, float | str]], max_batch_size: int = 20
    ) -> List[List[Mapping[str, float | str]]]:
        """
        create optimal batches for processing based on chunk characteristics.

        groups chunks by duration and complexity to optimize api usage.
        """
        # sort chunks by duration for better batching
        sorted_chunks = sorted(
            chunks, key=lambda x: float(x["end"]) - float(x["start"])
        )

        batches = []
        current_batch = []
        current_batch_duration = 0.0
        max_batch_duration = 300.0  # 5 minutes per batch

        for chunk in sorted_chunks:
            duration = float(chunk["end"]) - float(chunk["start"])

            # check if adding this chunk would exceed limits
            if (
                len(current_batch) >= max_batch_size
                or current_batch_duration + duration > max_batch_duration
            ):
                if current_batch:
                    batches.append(current_batch)
                    current_batch = []
                    current_batch_duration = 0.0

            current_batch.append(chunk)
            current_batch_duration += duration

        # add final batch
        if current_batch:
            batches.append(current_batch)

        logger().info(
            f"created {len(batches)} optimal batches from {len(chunks)} chunks"
        )
        return batches

    async def _rate_limited_submit(
        self,
        transcriber: aai.Transcriber,
        chunk: Mapping[str, float | str],
        semaphore: asyncio.Semaphore,
        rate_limiter: "RateLimiter",
    ) -> Dict:
        """
        submit a single transcription job with rate limiting.
        """
        async with semaphore:
            await rate_limiter.acquire()

            try:
                path = str(chunk["path"])
                logger().debug(f"submitting rate-limited job for: {path}")

                # submit job (non-blocking)
                transcript = transcriber.submit(path)

                return {
                    "transcript_id": transcript.id,
                    "chunk_metadata": chunk,
                    "submitted_at": time.time(),
                }

            except Exception as e:
                logger().error(f"failed to submit job for {chunk['path']}: {e}")
                return {
                    "transcript_id": None,
                    "chunk_metadata": chunk,
                    "submitted_at": time.time(),
                    "error": str(e),
                }


class RateLimiter:
    """
    intelligent rate limiter for assemblyai api calls.

    implements token bucket algorithm with adaptive rate adjustment.
    """

    def __init__(self, requests_per_second: float = 10.0, burst_size: int = 20):
        self.requests_per_second = requests_per_second
        self.burst_size = burst_size
        self.tokens = burst_size
        self.last_update = time.time()
        self._lock = asyncio.Lock()

    async def acquire(self):
        """acquire a token for making an api request."""
        async with self._lock:
            now = time.time()
            elapsed = now - self.last_update

            # add tokens based on elapsed time
            self.tokens = min(
                self.burst_size, self.tokens + elapsed * self.requests_per_second
            )
            self.last_update = now

            if self.tokens >= 1.0:
                self.tokens -= 1.0
                return

            # wait for next token
            wait_time = (1.0 - self.tokens) / self.requests_per_second
            await asyncio.sleep(wait_time)
            self.tokens = 0.0


class AsyncTranscriptionMonitor:
    """
    real-time monitoring and progress tracking for async transcription operations.

    provides detailed metrics, performance tracking, and progress callbacks.
    """

    def __init__(self):
        self.start_time = None
        self.total_chunks = 0
        self.completed_chunks = 0
        self.failed_chunks = 0
        self.submission_times = []
        self.completion_times = []
        self.api_errors = []

    def start_monitoring(self, total_chunks: int):
        """start monitoring a transcription session."""
        self.start_time = time.time()
        self.total_chunks = total_chunks
        self.completed_chunks = 0
        self.failed_chunks = 0
        self.submission_times = []
        self.completion_times = []
        self.api_errors = []

        logger().info(f"starting transcription monitoring for {total_chunks} chunks")

    def record_submission(self, chunk_id: str):
        """record when a chunk is submitted for transcription."""
        self.submission_times.append(
            {
                "chunk_id": chunk_id,
                "timestamp": time.time(),
            }
        )

    def record_completion(self, chunk_id: str, success: bool, error: str = None):
        """record when a chunk transcription completes."""
        completion_time = time.time()
        self.completion_times.append(
            {
                "chunk_id": chunk_id,
                "timestamp": completion_time,
                "success": success,
                "error": error,
            }
        )

        if success:
            self.completed_chunks += 1
        else:
            self.failed_chunks += 1
            if error:
                self.api_errors.append(error)

    def get_progress_stats(self) -> Dict:
        """get current progress statistics."""
        if not self.start_time:
            return {}

        elapsed_time = time.time() - self.start_time
        completion_rate = (
            self.completed_chunks / elapsed_time if elapsed_time > 0 else 0
        )

        # estimate time remaining
        remaining_chunks = (
            self.total_chunks - self.completed_chunks - self.failed_chunks
        )
        eta_seconds = remaining_chunks / completion_rate if completion_rate > 0 else 0

        return {
            "total_chunks": self.total_chunks,
            "completed": self.completed_chunks,
            "failed": self.failed_chunks,
            "remaining": remaining_chunks,
            "progress_percent": (
                (self.completed_chunks / self.total_chunks * 100)
                if self.total_chunks > 0
                else 0
            ),
            "elapsed_time": elapsed_time,
            "completion_rate": completion_rate,
            "eta_seconds": eta_seconds,
            "api_errors": len(self.api_errors),
        }

    def log_progress_update(self):
        """log a progress update with current statistics."""
        stats = self.get_progress_stats()
        if not stats:
            return

        logger().info(
            f"transcription progress: {stats['completed']}/{stats['total_chunks']} "
            f"({stats['progress_percent']:.1f}%) - "
            f"rate: {stats['completion_rate']:.1f} chunks/sec - "
            f"eta: {stats['eta_seconds']:.0f}s"
        )

        if stats["failed"] > 0:
            logger().warning(f"failed chunks: {stats['failed']}")

    def get_final_report(self) -> Dict:
        """get final performance report."""
        if not self.start_time:
            return {}

        total_time = time.time() - self.start_time
        success_rate = (
            (self.completed_chunks / self.total_chunks * 100)
            if self.total_chunks > 0
            else 0
        )

        # calculate average processing time per chunk
        avg_processing_time = (
            total_time / self.total_chunks if self.total_chunks > 0 else 0
        )

        return {
            "total_chunks": self.total_chunks,
            "completed": self.completed_chunks,
            "failed": self.failed_chunks,
            "success_rate": success_rate,
            "total_time": total_time,
            "avg_time_per_chunk": avg_processing_time,
            "throughput": self.total_chunks / total_time if total_time > 0 else 0,
            "api_errors": self.api_errors,
        }
