{"basic_cost_tracking": {"description": "basic cost tracking configuration with default settings", "config": {"input": {"source_path": "video.mp4", "source_lang": "en"}, "translation": {"target_lang": "es", "provider": "gemini"}, "models": {"tts_provider": "azure", "audio_quality": "high"}, "cost_tracking": {"enabled": true, "cost_estimation": true, "detailed_reporting": true}}}, "budget_managed_production": {"description": "production configuration with strict budget management", "config": {"input": {"source_path": "production_video.mp4", "source_lang": "en"}, "translation": {"target_lang": "es", "provider": "gemini"}, "models": {"tts_provider": "azure", "audio_quality": "maximum"}, "cost_tracking": {"enabled": true, "cost_estimation": true, "daily_budget_limit": 100.0, "weekly_budget_limit": 500.0, "monthly_budget_limit": 2000.0, "per_job_budget_limit": 50.0, "budget_enforcement": true, "alert_thresholds": [0.7, 0.85, 0.95, 1.0], "notification_emails": ["<EMAIL>", "<EMAIL>"], "detailed_reporting": true, "export_cost_data": true}}}, "development_testing": {"description": "development configuration with relaxed budgets for testing", "config": {"input": {"source_path": "test_video.mp4", "source_lang": "en"}, "translation": {"target_lang": "es", "provider": "gemini"}, "models": {"tts_provider": "azure", "audio_quality": "standard"}, "cost_tracking": {"enabled": true, "cost_estimation": true, "daily_budget_limit": 20.0, "per_job_budget_limit": 5.0, "budget_enforcement": false, "alert_thresholds": [0.8, 0.9, 1.0], "detailed_reporting": false, "export_cost_data": false}}}, "cost_optimized": {"description": "cost-optimized configuration using cheaper models and settings", "config": {"input": {"source_path": "video.mp4", "source_lang": "en"}, "translation": {"target_lang": "es", "provider": "gemini"}, "models": {"tts_provider": "yandex", "audio_quality": "standard"}, "cost_tracking": {"enabled": true, "cost_estimation": true, "daily_budget_limit": 30.0, "per_job_budget_limit": 10.0, "budget_enforcement": true, "alert_thresholds": [0.8, 0.9, 1.0], "detailed_reporting": true, "export_cost_data": true, "pricing_config_file": "cost_optimized_pricing.json"}}}, "high_volume_batch": {"description": "configuration for high-volume batch processing with volume discounts", "config": {"input": {"source_path": "batch_video.mp4", "source_lang": "en"}, "translation": {"target_lang": "es", "provider": "gemini"}, "models": {"tts_provider": "azure", "audio_quality": "high"}, "performance": {"max_workers": 8}, "cost_tracking": {"enabled": true, "cost_estimation": true, "daily_budget_limit": 500.0, "weekly_budget_limit": 3000.0, "monthly_budget_limit": 10000.0, "per_job_budget_limit": 100.0, "budget_enforcement": true, "alert_thresholds": [0.8, 0.9, 1.0], "notification_emails": ["<EMAIL>"], "detailed_reporting": true, "export_cost_data": true, "pricing_config_file": "volume_discount_pricing.json"}}}, "multi_language_enterprise": {"description": "enterprise configuration for multi-language dubbing with comprehensive tracking", "config": {"input": {"source_path": "enterprise_video.mp4", "source_lang": "en"}, "translation": {"target_lang": "es", "provider": "gemini"}, "models": {"tts_provider": "elevenlabs", "audio_quality": "maximum"}, "cost_tracking": {"enabled": true, "cost_estimation": true, "daily_budget_limit": 1000.0, "weekly_budget_limit": 5000.0, "monthly_budget_limit": 20000.0, "per_job_budget_limit": 200.0, "budget_enforcement": true, "alert_thresholds": [0.75, 0.85, 0.95, 1.0], "notification_emails": ["<EMAIL>", "<EMAIL>", "<EMAIL>"], "detailed_reporting": true, "export_cost_data": true, "budget_config_file": "enterprise_budget.json"}}}}