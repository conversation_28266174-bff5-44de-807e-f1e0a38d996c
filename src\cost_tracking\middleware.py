"""cost tracking middleware and decorators for automatic cost calculation.

this module provides thread-safe cost tracking middleware that can wrap ai provider
calls and automatically calculate costs without impacting performance.
"""

import asyncio
import functools
import threading
import time
from contextlib import contextmanager
from typing import Any, Callable, Dict, Optional, TypeVar, Union

from src.utils.logger import logger
from .models import CostEntry, CostBreakdown, ProviderType, CostType
from .calculators import (
    AssemblyAICostCalculator,
    GeminiCostCalculator,
    TTSCostCalculator,
    ProcessingCostCalculator,
)

F = TypeVar('F', bound=Callable[..., Any])


class CostTracker:
    """thread-safe cost tracker for accumulating costs during job processing."""
    
    def __init__(self, run_id: str):
        self.run_id = run_id
        self.cost_breakdown = CostBreakdown()
        self._lock = threading.Lock()
        self._calculators: Dict[ProviderType, Any] = {}
        self._start_time: Optional[float] = None
        self._end_time: Optional[float] = None
    
    def set_calculator(self, provider: ProviderType, calculator: Any) -> None:
        """set cost calculator for a specific provider."""
        with self._lock:
            self._calculators[provider] = calculator
    
    def start_tracking(self) -> None:
        """start cost tracking for the job."""
        with self._lock:
            self._start_time = time.time()
            logger().debug(f"started cost tracking for run {self.run_id}")
    
    def stop_tracking(self) -> None:
        """stop cost tracking for the job."""
        with self._lock:
            self._end_time = time.time()
            duration = self._end_time - self._start_time if self._start_time else 0
            logger().info(
                f"stopped cost tracking for run {self.run_id}. "
                f"total cost: ${self.cost_breakdown.total_cost:.4f}, "
                f"duration: {duration:.2f}s"
            )
    
    def add_cost_entry(self, entry: CostEntry) -> None:
        """add a cost entry to the tracker."""
        with self._lock:
            self.cost_breakdown.add_cost_entry(entry)
            logger().debug(
                f"added cost entry: {entry.provider.value} - "
                f"${entry.amount:.4f} ({entry.units_consumed} {entry.cost_type.value})"
            )
    
    def get_total_cost(self) -> float:
        """get current total cost."""
        with self._lock:
            return float(self.cost_breakdown.total_cost)
    
    def get_provider_cost(self, provider: ProviderType) -> float:
        """get total cost for a specific provider."""
        with self._lock:
            return float(self.cost_breakdown.get_provider_total(provider))
    
    def get_cost_summary(self) -> Dict[str, Any]:
        """get comprehensive cost summary."""
        with self._lock:
            return {
                "run_id": self.run_id,
                "total_cost": float(self.cost_breakdown.total_cost),
                "assemblyai_cost": float(self.cost_breakdown.get_provider_total(ProviderType.ASSEMBLYAI)),
                "gemini_cost": float(self.cost_breakdown.get_provider_total(ProviderType.GEMINI)),
                "azure_tts_cost": float(self.cost_breakdown.get_provider_total(ProviderType.AZURE_TTS)),
                "elevenlabs_tts_cost": float(self.cost_breakdown.get_provider_total(ProviderType.ELEVENLABS_TTS)),
                "yandex_tts_cost": float(self.cost_breakdown.get_provider_total(ProviderType.YANDEX_TTS)),
                "gpu_processing_cost": float(self.cost_breakdown.get_provider_total(ProviderType.GPU_PROCESSING)),
                "cpu_processing_cost": float(self.cost_breakdown.get_provider_total(ProviderType.CPU_PROCESSING)),
                "start_time": self._start_time,
                "end_time": self._end_time,
                "duration": self._end_time - self._start_time if self._start_time and self._end_time else None,
            }


# global cost tracker registry
_cost_trackers: Dict[str, CostTracker] = {}
_tracker_lock = threading.Lock()


def get_cost_tracker(run_id: str) -> Optional[CostTracker]:
    """get cost tracker for a specific run."""
    with _tracker_lock:
        return _cost_trackers.get(run_id)


def create_cost_tracker(run_id: str) -> CostTracker:
    """create and register a new cost tracker."""
    with _tracker_lock:
        if run_id in _cost_trackers:
            logger().warning(f"cost tracker for run {run_id} already exists")
            return _cost_trackers[run_id]
        
        tracker = CostTracker(run_id)
        _cost_trackers[run_id] = tracker
        logger().debug(f"created cost tracker for run {run_id}")
        return tracker


def remove_cost_tracker(run_id: str) -> None:
    """remove cost tracker for a run."""
    with _tracker_lock:
        if run_id in _cost_trackers:
            del _cost_trackers[run_id]
            logger().debug(f"removed cost tracker for run {run_id}")


@contextmanager
def cost_tracking_context(run_id: str):
    """context manager for cost tracking."""
    tracker = create_cost_tracker(run_id)
    tracker.start_tracking()
    try:
        yield tracker
    finally:
        tracker.stop_tracking()


def cost_tracking_decorator(
    provider: ProviderType,
    cost_type: CostType,
    extract_metrics: Callable[[Any, Any], Dict[str, Any]],
    run_id_param: str = "run_id",
):
    """
    decorator for automatic cost tracking of provider operations.
    
    args:
        provider: the ai provider type
        cost_type: the type of cost being tracked
        extract_metrics: function to extract cost metrics from function args/result
        run_id_param: parameter name that contains the run_id
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # extract run_id
            run_id = kwargs.get(run_id_param)
            if not run_id:
                # try to find run_id in args if it's a method call
                if hasattr(args[0], 'run_id'):
                    run_id = getattr(args[0], 'run_id')
            
            if not run_id:
                logger().warning(f"no run_id found for cost tracking in {func.__name__}")
                return func(*args, **kwargs)
            
            # get cost tracker
            tracker = get_cost_tracker(run_id)
            if not tracker:
                logger().warning(f"no cost tracker found for run {run_id}")
                return func(*args, **kwargs)
            
            # execute function and track time
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                end_time = time.time()
                
                # extract metrics and calculate cost
                try:
                    metrics = extract_metrics(args, kwargs, result)
                    if provider in tracker._calculators:
                        calculator = tracker._calculators[provider]
                        cost_entry = calculator.calculate_cost(**metrics)
                        tracker.add_cost_entry(cost_entry)
                    else:
                        logger().warning(f"no calculator found for provider {provider.value}")
                
                except Exception as e:
                    logger().error(f"error calculating cost for {func.__name__}: {e}")
                
                return result
                
            except Exception as e:
                logger().error(f"error in {func.__name__}: {e}")
                raise
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            # extract run_id
            run_id = kwargs.get(run_id_param)
            if not run_id:
                # try to find run_id in args if it's a method call
                if hasattr(args[0], 'run_id'):
                    run_id = getattr(args[0], 'run_id')
            
            if not run_id:
                logger().warning(f"no run_id found for cost tracking in {func.__name__}")
                return await func(*args, **kwargs)
            
            # get cost tracker
            tracker = get_cost_tracker(run_id)
            if not tracker:
                logger().warning(f"no cost tracker found for run {run_id}")
                return await func(*args, **kwargs)
            
            # execute function and track time
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                end_time = time.time()
                
                # extract metrics and calculate cost
                try:
                    metrics = extract_metrics(args, kwargs, result)
                    if provider in tracker._calculators:
                        calculator = tracker._calculators[provider]
                        cost_entry = calculator.calculate_cost(**metrics)
                        tracker.add_cost_entry(cost_entry)
                    else:
                        logger().warning(f"no calculator found for provider {provider.value}")
                
                except Exception as e:
                    logger().error(f"error calculating cost for {func.__name__}: {e}")
                
                return result
                
            except Exception as e:
                logger().error(f"error in {func.__name__}: {e}")
                raise
        
        # return appropriate wrapper based on whether function is async
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return wrapper
    
    return decorator


def processing_time_tracker(
    operation_type: str,
    device_type: str = "gpu",
    run_id_param: str = "run_id",
):
    """decorator for tracking processing time costs."""
    def extract_metrics(args, kwargs, result):
        return {
            "processing_time_seconds": getattr(result, 'processing_time', 0),
            "operation_type": operation_type,
            "device_type": device_type,
        }
    
    provider = ProviderType.GPU_PROCESSING if device_type == "gpu" else ProviderType.CPU_PROCESSING
    return cost_tracking_decorator(
        provider=provider,
        cost_type=CostType.PROCESSING_TIME,
        extract_metrics=extract_metrics,
        run_id_param=run_id_param,
    )
