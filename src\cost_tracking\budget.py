"""budget management and alert system for cost tracking.

this module implements configurable budget limits, real-time budget monitoring,
alert system for approaching limits, and cost enforcement mechanisms.
"""

import json
import time
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable

from src.utils.logger import logger
from .models import BudgetConfig, CostEntry, ProviderType


class AlertLevel(str, Enum):
    """budget alert levels."""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EXCEEDED = "exceeded"


class BudgetAlert:
    """budget alert notification."""
    
    def __init__(
        self,
        alert_level: AlertLevel,
        budget_type: str,
        current_amount: Decimal,
        limit_amount: Decimal,
        threshold_percentage: float,
        run_id: Optional[str] = None,
        provider: Optional[ProviderType] = None,
    ):
        self.alert_level = alert_level
        self.budget_type = budget_type
        self.current_amount = current_amount
        self.limit_amount = limit_amount
        self.threshold_percentage = threshold_percentage
        self.run_id = run_id
        self.provider = provider
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """convert alert to dictionary."""
        return {
            "alert_level": self.alert_level.value,
            "budget_type": self.budget_type,
            "current_amount": float(self.current_amount),
            "limit_amount": float(self.limit_amount),
            "threshold_percentage": self.threshold_percentage,
            "run_id": self.run_id,
            "provider": self.provider.value if self.provider else None,
            "timestamp": self.timestamp.isoformat(),
            "message": self.get_message(),
        }
    
    def get_message(self) -> str:
        """get human-readable alert message."""
        percentage = (self.current_amount / self.limit_amount * 100) if self.limit_amount > 0 else 0
        
        if self.alert_level == AlertLevel.EXCEEDED:
            return (
                f"budget exceeded! {self.budget_type} budget of ${self.limit_amount:.2f} "
                f"has been exceeded by ${self.current_amount - self.limit_amount:.2f} "
                f"({percentage:.1f}% of limit)"
            )
        else:
            return (
                f"budget alert: {self.budget_type} budget is at {percentage:.1f}% "
                f"(${self.current_amount:.2f} of ${self.limit_amount:.2f} limit)"
            )


class BudgetManager:
    """manages budget limits, monitoring, and alerts."""
    
    def __init__(self, config: BudgetConfig, storage_path: Optional[Path] = None):
        self.config = config
        self.storage_path = storage_path or Path("data/budget_tracking.json")
        self.storage_path.parent.mkdir(parents=True, exist_ok=True)
        
        # budget tracking data
        self._daily_spending: Dict[str, Decimal] = {}
        self._weekly_spending: Dict[str, Decimal] = {}
        self._monthly_spending: Dict[str, Decimal] = {}
        self._job_spending: Dict[str, Decimal] = {}
        
        # alert callbacks
        self._alert_callbacks: List[Callable[[BudgetAlert], None]] = []
        
        # load existing data
        self._load_budget_data()
    
    def add_alert_callback(self, callback: Callable[[BudgetAlert], None]) -> None:
        """add callback function for budget alerts."""
        self._alert_callbacks.append(callback)
    
    def track_cost(self, cost_entry: CostEntry, run_id: str) -> List[BudgetAlert]:
        """
        track a cost entry and check for budget violations.
        
        returns list of alerts generated.
        """
        alerts = []
        today = datetime.now().strftime("%Y-%m-%d")
        week = self._get_week_key()
        month = datetime.now().strftime("%Y-%m")
        
        # update spending tracking
        self._daily_spending[today] = self._daily_spending.get(today, Decimal("0")) + cost_entry.amount
        self._weekly_spending[week] = self._weekly_spending.get(week, Decimal("0")) + cost_entry.amount
        self._monthly_spending[month] = self._monthly_spending.get(month, Decimal("0")) + cost_entry.amount
        self._job_spending[run_id] = self._job_spending.get(run_id, Decimal("0")) + cost_entry.amount
        
        # check budget limits and generate alerts
        if self.config.daily_limit:
            alerts.extend(self._check_budget_limit(
                "daily", self._daily_spending[today], self.config.daily_limit, run_id
            ))
        
        if self.config.weekly_limit:
            alerts.extend(self._check_budget_limit(
                "weekly", self._weekly_spending[week], self.config.weekly_limit, run_id
            ))
        
        if self.config.monthly_limit:
            alerts.extend(self._check_budget_limit(
                "monthly", self._monthly_spending[month], self.config.monthly_limit, run_id
            ))
        
        if self.config.per_job_limit:
            alerts.extend(self._check_budget_limit(
                "per_job", self._job_spending[run_id], self.config.per_job_limit, run_id
            ))
        
        # save updated data
        self._save_budget_data()
        
        # trigger alert callbacks
        for alert in alerts:
            for callback in self._alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    logger().error(f"error in budget alert callback: {e}")
        
        return alerts
    
    def check_budget_before_operation(
        self,
        estimated_cost: Decimal,
        run_id: str
    ) -> tuple[bool, List[BudgetAlert]]:
        """
        check if an operation would exceed budget limits.
        
        returns (can_proceed, alerts)
        """
        if not self.config.enforcement_enabled:
            return True, []
        
        alerts = []
        can_proceed = True
        
        today = datetime.now().strftime("%Y-%m-%d")
        week = self._get_week_key()
        month = datetime.now().strftime("%Y-%m")
        
        current_daily = self._daily_spending.get(today, Decimal("0"))
        current_weekly = self._weekly_spending.get(week, Decimal("0"))
        current_monthly = self._monthly_spending.get(month, Decimal("0"))
        current_job = self._job_spending.get(run_id, Decimal("0"))
        
        # check if operation would exceed limits
        if self.config.daily_limit and (current_daily + estimated_cost) > self.config.daily_limit:
            can_proceed = False
            alerts.append(BudgetAlert(
                AlertLevel.EXCEEDED,
                "daily",
                current_daily + estimated_cost,
                self.config.daily_limit,
                100.0,
                run_id
            ))
        
        if self.config.weekly_limit and (current_weekly + estimated_cost) > self.config.weekly_limit:
            can_proceed = False
            alerts.append(BudgetAlert(
                AlertLevel.EXCEEDED,
                "weekly",
                current_weekly + estimated_cost,
                self.config.weekly_limit,
                100.0,
                run_id
            ))
        
        if self.config.monthly_limit and (current_monthly + estimated_cost) > self.config.monthly_limit:
            can_proceed = False
            alerts.append(BudgetAlert(
                AlertLevel.EXCEEDED,
                "monthly",
                current_monthly + estimated_cost,
                self.config.monthly_limit,
                100.0,
                run_id
            ))
        
        if self.config.per_job_limit and (current_job + estimated_cost) > self.config.per_job_limit:
            can_proceed = False
            alerts.append(BudgetAlert(
                AlertLevel.EXCEEDED,
                "per_job",
                current_job + estimated_cost,
                self.config.per_job_limit,
                100.0,
                run_id
            ))
        
        return can_proceed, alerts
    
    def get_budget_status(self) -> Dict[str, Any]:
        """get current budget status."""
        today = datetime.now().strftime("%Y-%m-%d")
        week = self._get_week_key()
        month = datetime.now().strftime("%Y-%m")
        
        status = {
            "daily": {
                "spent": float(self._daily_spending.get(today, Decimal("0"))),
                "limit": float(self.config.daily_limit) if self.config.daily_limit else None,
                "remaining": None,
                "percentage": None,
            },
            "weekly": {
                "spent": float(self._weekly_spending.get(week, Decimal("0"))),
                "limit": float(self.config.weekly_limit) if self.config.weekly_limit else None,
                "remaining": None,
                "percentage": None,
            },
            "monthly": {
                "spent": float(self._monthly_spending.get(month, Decimal("0"))),
                "limit": float(self.config.monthly_limit) if self.config.monthly_limit else None,
                "remaining": None,
                "percentage": None,
            },
        }
        
        # calculate remaining and percentages
        for period, data in status.items():
            if data["limit"] is not None:
                data["remaining"] = data["limit"] - data["spent"]
                data["percentage"] = (data["spent"] / data["limit"] * 100) if data["limit"] > 0 else 0
        
        return status
    
    def _check_budget_limit(
        self,
        budget_type: str,
        current_amount: Decimal,
        limit_amount: Decimal,
        run_id: str
    ) -> List[BudgetAlert]:
        """check if budget limit is approaching or exceeded."""
        alerts = []
        percentage = (current_amount / limit_amount * 100) if limit_amount > 0 else 0
        
        if current_amount > limit_amount:
            # budget exceeded
            alerts.append(BudgetAlert(
                AlertLevel.EXCEEDED,
                budget_type,
                current_amount,
                limit_amount,
                percentage,
                run_id
            ))
        else:
            # check alert thresholds
            for threshold in sorted(self.config.alert_thresholds, reverse=True):
                if percentage >= (threshold * 100):
                    alert_level = AlertLevel.CRITICAL if threshold >= 0.9 else AlertLevel.WARNING
                    alerts.append(BudgetAlert(
                        alert_level,
                        budget_type,
                        current_amount,
                        limit_amount,
                        threshold * 100,
                        run_id
                    ))
                    break
        
        return alerts
    
    def _get_week_key(self) -> str:
        """get week key for tracking weekly spending."""
        now = datetime.now()
        # get monday of current week
        monday = now - timedelta(days=now.weekday())
        return monday.strftime("%Y-W%U")
    
    def _load_budget_data(self) -> None:
        """load budget tracking data from storage."""
        try:
            if self.storage_path.exists():
                with open(self.storage_path, 'r') as f:
                    data = json.load(f)
                
                # convert string keys and values back to appropriate types
                self._daily_spending = {k: Decimal(str(v)) for k, v in data.get("daily", {}).items()}
                self._weekly_spending = {k: Decimal(str(v)) for k, v in data.get("weekly", {}).items()}
                self._monthly_spending = {k: Decimal(str(v)) for k, v in data.get("monthly", {}).items()}
                self._job_spending = {k: Decimal(str(v)) for k, v in data.get("jobs", {}).items()}
                
                logger().debug("loaded budget tracking data")
        except Exception as e:
            logger().warning(f"failed to load budget data: {e}")
    
    def _save_budget_data(self) -> None:
        """save budget tracking data to storage."""
        try:
            data = {
                "daily": {k: float(v) for k, v in self._daily_spending.items()},
                "weekly": {k: float(v) for k, v in self._weekly_spending.items()},
                "monthly": {k: float(v) for k, v in self._monthly_spending.items()},
                "jobs": {k: float(v) for k, v in self._job_spending.items()},
                "last_updated": datetime.now().isoformat(),
            }
            
            with open(self.storage_path, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger().error(f"failed to save budget data: {e}")
