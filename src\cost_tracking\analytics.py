"""cost analytics and reporting functionality.

this module provides cost analytics with filtering by date range, provider, job type,
detailed cost breakdowns, export functionality, and cost trend analysis.
"""

import csv
import json
from datetime import datetime, timedelta
from decimal import Decimal
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

from src.utils.logger import logger
from .models import CostEntry, CostBreakdown, ProviderType, CostType


class CostReport:
    """comprehensive cost report with analytics."""
    
    def __init__(
        self,
        start_date: datetime,
        end_date: datetime,
        total_cost: Decimal,
        cost_by_provider: Dict[str, Decimal],
        cost_by_type: Dict[str, Decimal],
        cost_by_day: Dict[str, Decimal],
        job_count: int,
        average_job_cost: Decimal,
    ):
        self.start_date = start_date
        self.end_date = end_date
        self.total_cost = total_cost
        self.cost_by_provider = cost_by_provider
        self.cost_by_type = cost_by_type
        self.cost_by_day = cost_by_day
        self.job_count = job_count
        self.average_job_cost = average_job_cost
        self.generated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """convert report to dictionary."""
        return {
            "start_date": self.start_date.isoformat(),
            "end_date": self.end_date.isoformat(),
            "total_cost": float(self.total_cost),
            "cost_by_provider": {k: float(v) for k, v in self.cost_by_provider.items()},
            "cost_by_type": {k: float(v) for k, v in self.cost_by_type.items()},
            "cost_by_day": {k: float(v) for k, v in self.cost_by_day.items()},
            "job_count": self.job_count,
            "average_job_cost": float(self.average_job_cost),
            "generated_at": self.generated_at.isoformat(),
        }
    
    def export_to_json(self, file_path: Path) -> None:
        """export report to json file."""
        with open(file_path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
        logger().info(f"exported cost report to {file_path}")
    
    def export_to_csv(self, file_path: Path) -> None:
        """export report to csv file."""
        with open(file_path, 'w', newline='') as f:
            writer = csv.writer(f)
            
            # write summary
            writer.writerow(["cost report summary"])
            writer.writerow(["start_date", self.start_date.isoformat()])
            writer.writerow(["end_date", self.end_date.isoformat()])
            writer.writerow(["total_cost", float(self.total_cost)])
            writer.writerow(["job_count", self.job_count])
            writer.writerow(["average_job_cost", float(self.average_job_cost)])
            writer.writerow([])
            
            # write cost by provider
            writer.writerow(["cost by provider"])
            writer.writerow(["provider", "cost"])
            for provider, cost in self.cost_by_provider.items():
                writer.writerow([provider, float(cost)])
            writer.writerow([])
            
            # write cost by type
            writer.writerow(["cost by type"])
            writer.writerow(["type", "cost"])
            for cost_type, cost in self.cost_by_type.items():
                writer.writerow([cost_type, float(cost)])
            writer.writerow([])
            
            # write daily costs
            writer.writerow(["daily costs"])
            writer.writerow(["date", "cost"])
            for date, cost in sorted(self.cost_by_day.items()):
                writer.writerow([date, float(cost)])
        
        logger().info(f"exported cost report to {file_path}")


class CostAnalytics:
    """cost analytics engine for generating reports and insights."""
    
    def __init__(self, data_directory: Path = None):
        self.data_directory = data_directory or Path("data/cost_analytics")
        self.data_directory.mkdir(parents=True, exist_ok=True)
    
    def generate_report(
        self,
        cost_entries: List[CostEntry],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        provider_filter: Optional[List[ProviderType]] = None,
        cost_type_filter: Optional[List[CostType]] = None,
    ) -> CostReport:
        """
        generate comprehensive cost report.
        
        args:
            cost_entries: list of cost entries to analyze
            start_date: filter entries after this date
            end_date: filter entries before this date
            provider_filter: filter by specific providers
            cost_type_filter: filter by specific cost types
        """
        # filter entries
        filtered_entries = self._filter_entries(
            cost_entries, start_date, end_date, provider_filter, cost_type_filter
        )
        
        if not filtered_entries:
            # return empty report
            return CostReport(
                start_date=start_date or datetime.min,
                end_date=end_date or datetime.max,
                total_cost=Decimal("0"),
                cost_by_provider={},
                cost_by_type={},
                cost_by_day={},
                job_count=0,
                average_job_cost=Decimal("0"),
            )
        
        # calculate analytics
        total_cost = sum(entry.amount for entry in filtered_entries)
        
        # cost by provider
        cost_by_provider = {}
        for entry in filtered_entries:
            provider = entry.provider.value
            cost_by_provider[provider] = cost_by_provider.get(provider, Decimal("0")) + entry.amount
        
        # cost by type
        cost_by_type = {}
        for entry in filtered_entries:
            cost_type = entry.cost_type.value
            cost_by_type[cost_type] = cost_by_type.get(cost_type, Decimal("0")) + entry.amount
        
        # cost by day
        cost_by_day = {}
        for entry in filtered_entries:
            date = datetime.fromtimestamp(entry.timestamp).strftime("%Y-%m-%d")
            cost_by_day[date] = cost_by_day.get(date, Decimal("0")) + entry.amount
        
        # job statistics
        unique_jobs = len(set(entry.metadata.get("run_id") for entry in filtered_entries if entry.metadata.get("run_id")))
        job_count = max(unique_jobs, 1)  # avoid division by zero
        average_job_cost = total_cost / job_count
        
        return CostReport(
            start_date=start_date or datetime.fromtimestamp(min(entry.timestamp for entry in filtered_entries)),
            end_date=end_date or datetime.fromtimestamp(max(entry.timestamp for entry in filtered_entries)),
            total_cost=total_cost,
            cost_by_provider=cost_by_provider,
            cost_by_type=cost_by_type,
            cost_by_day=cost_by_day,
            job_count=job_count,
            average_job_cost=average_job_cost,
        )
    
    def analyze_cost_trends(
        self,
        cost_entries: List[CostEntry],
        days: int = 30,
    ) -> Dict[str, Any]:
        """
        analyze cost trends over the specified number of days.
        
        returns trend analysis including growth rates and patterns.
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # filter entries to the specified period
        filtered_entries = self._filter_entries(cost_entries, start_date, end_date)
        
        if not filtered_entries:
            return {"error": "no cost data available for the specified period"}
        
        # group by day
        daily_costs = {}
        for entry in filtered_entries:
            date = datetime.fromtimestamp(entry.timestamp).strftime("%Y-%m-%d")
            daily_costs[date] = daily_costs.get(date, Decimal("0")) + entry.amount
        
        # calculate trends
        dates = sorted(daily_costs.keys())
        costs = [float(daily_costs[date]) for date in dates]
        
        # simple linear trend calculation
        if len(costs) > 1:
            x_values = list(range(len(costs)))
            n = len(costs)
            sum_x = sum(x_values)
            sum_y = sum(costs)
            sum_xy = sum(x * y for x, y in zip(x_values, costs))
            sum_x2 = sum(x * x for x in x_values)
            
            # linear regression slope
            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x) if (n * sum_x2 - sum_x * sum_x) != 0 else 0
            
            # trend direction
            if slope > 0.1:
                trend_direction = "increasing"
            elif slope < -0.1:
                trend_direction = "decreasing"
            else:
                trend_direction = "stable"
        else:
            slope = 0
            trend_direction = "insufficient_data"
        
        # calculate weekly comparison
        current_week_start = end_date - timedelta(days=7)
        previous_week_start = current_week_start - timedelta(days=7)
        
        current_week_cost = sum(
            entry.amount for entry in filtered_entries
            if datetime.fromtimestamp(entry.timestamp) >= current_week_start
        )
        
        previous_week_cost = sum(
            entry.amount for entry in filtered_entries
            if previous_week_start <= datetime.fromtimestamp(entry.timestamp) < current_week_start
        )
        
        week_over_week_change = 0
        if previous_week_cost > 0:
            week_over_week_change = float((current_week_cost - previous_week_cost) / previous_week_cost * 100)
        
        return {
            "period_days": days,
            "total_cost": float(sum(daily_costs.values())),
            "average_daily_cost": float(sum(daily_costs.values()) / len(daily_costs)) if daily_costs else 0,
            "trend_direction": trend_direction,
            "trend_slope": slope,
            "current_week_cost": float(current_week_cost),
            "previous_week_cost": float(previous_week_cost),
            "week_over_week_change_percent": week_over_week_change,
            "daily_costs": {date: float(cost) for date, cost in daily_costs.items()},
            "highest_cost_day": max(daily_costs.items(), key=lambda x: x[1]) if daily_costs else None,
            "lowest_cost_day": min(daily_costs.items(), key=lambda x: x[1]) if daily_costs else None,
        }
    
    def get_provider_efficiency_metrics(
        self,
        cost_entries: List[CostEntry],
    ) -> Dict[str, Any]:
        """analyze efficiency metrics for each provider."""
        provider_metrics = {}
        
        for provider in ProviderType:
            provider_entries = [e for e in cost_entries if e.provider == provider]
            if not provider_entries:
                continue
            
            total_cost = sum(entry.amount for entry in provider_entries)
            total_units = sum(entry.units_consumed for entry in provider_entries)
            avg_unit_cost = total_cost / total_units if total_units > 0 else Decimal("0")
            
            # calculate cost per operation
            operation_count = len(provider_entries)
            avg_cost_per_operation = total_cost / operation_count if operation_count > 0 else Decimal("0")
            
            provider_metrics[provider.value] = {
                "total_cost": float(total_cost),
                "total_units": total_units,
                "average_unit_cost": float(avg_unit_cost),
                "operation_count": operation_count,
                "average_cost_per_operation": float(avg_cost_per_operation),
                "cost_percentage": float(total_cost / sum(e.amount for e in cost_entries) * 100) if cost_entries else 0,
            }
        
        return provider_metrics
    
    def _filter_entries(
        self,
        entries: List[CostEntry],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        provider_filter: Optional[List[ProviderType]] = None,
        cost_type_filter: Optional[List[CostType]] = None,
    ) -> List[CostEntry]:
        """filter cost entries based on criteria."""
        filtered = entries
        
        if start_date:
            start_timestamp = start_date.timestamp()
            filtered = [e for e in filtered if e.timestamp >= start_timestamp]
        
        if end_date:
            end_timestamp = end_date.timestamp()
            filtered = [e for e in filtered if e.timestamp <= end_timestamp]
        
        if provider_filter:
            filtered = [e for e in filtered if e.provider in provider_filter]
        
        if cost_type_filter:
            filtered = [e for e in filtered if e.cost_type in cost_type_filter]
        
        return filtered
