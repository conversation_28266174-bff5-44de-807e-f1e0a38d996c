# aizen platform - ai dubbing system

<div align="center">

![python](https://img.shields.io/badge/python-3.11.11-blue.svg)
![license](https://img.shields.io/badge/license-none-red.svg)
![status](https://img.shields.io/badge/status-development-yellow.svg)

**an advanced ai-powered video dubbing system that automatically translates and synchronizes audio dialogue into different languages**

[features](#-features) • [quick start](#-quick-start) • [installation](#-installation) • [usage](#-usage) • [configuration](#-configuration)

</div>

---

## 🎯 features

### core dubbing capabilities

-   **🎬 video processing**: supports mp4 video files with automatic audio/video separation
-   **🎵 vocal isolation**: uses demucs for high-quality vocal separation from background music
-   **👥 speaker diarization**: pyannote-based speaker identification and segmentation
-   **🗣️ speech-to-text**: async assemblyai integration with 2-5x performance improvement and advanced features
-   **🌍 translation**: google gemini-powered translation to any target language
-   **🎤 text-to-speech**: multiple tts providers (yandex, azure, elevenlabs) with maximum audio quality
-   **⚡ gpu acceleration**: cuda support for faster processing
-   **📝 subtitles**: automatic generation of srt and vtt subtitle files

### advanced features

-   **🔄 incremental updates**: smart re-processing with metadata caching
-   **🆔 run organization**: uuid-based file organization for isolated dubbing sessions
-   **💰 comprehensive cost tracking**: real-time cost monitoring, budget management, and detailed analytics
-   **📊 cost estimation**: pre-execution cost predictions for budget planning
-   **🚨 budget alerts**: configurable spending limits with enforcement and notifications
-   **📈 cost analytics**: detailed reporting, trend analysis, and cost optimization insights

---

## 🚀 quick start

```bash
# 1. clone and setup environment
git clone <repository-url>
cd aizen_platform/server
uv venv --python=3.11.11
uv sync

# 2. configure environment variables
cp .env.example .env
# edit .env with your api keys

# 3. run basic dubbing with cost tracking (english → russian)
python -m src.core.main --input_file path/to/video.mp4 --target_language ru --daily_budget 25.0
```

**output**:

-   run id: `2292263e-3e65-4d45-acdd-6bd1c7faaf08` (displayed during processing)
-   dubbed video saved in: `data/outputs/2292263e-3e65-4d45-acdd-6bd1c7faaf08/`

---

## 📦 installation

### prerequisites

| requirement    | version | purpose                           |
| -------------- | ------- | --------------------------------- |
| python         | 3.11.11 | core runtime (strict requirement) |
| ffmpeg         | ≥ 5.0   | video/audio processing            |
| nvidia drivers | ≥ 550   | gpu acceleration (optional)       |
| git lfs        | latest  | large model files (optional)      |

### system dependencies

**ubuntu/debian:**

```bash
sudo apt update
sudo apt install build-essential libsndfile1 ffmpeg
```

**windows:**

```powershell
# install ffmpeg via chocolatey
choco install ffmpeg
```

### python environment

```bash
# create virtual environment with exact python version
uv venv --python=3.11.11
source .venv/bin/activate  # linux/mac
# or .venv\Scripts\activate  # windows

# install dependencies
uv sync
```

---

## 🎮 usage

### basic usage

```bash
# minimal command (uzbek)
python -m src.core.main --input_file video.mp4 --target_language uz

# with gpu acceleration (russian)
python -m src.core.main --input_file video.mp4 --target_language ru --device cuda

# high quality with subtitles (english)
python -m src.core.main --input_file video.mp4 --target_language en --dubbed_subtitles --original_subtitles

# async assemblyai provides 2-5x faster transcription with advanced features (uzbek)
python -m src.core.main --input_file video.mp4 --target_language uz

# with cost tracking and budget management
python -m src.core.main --input_file video.mp4 --target_language uz --daily_budget 50.0 --cost_report
```

### advanced usage

```bash
# custom tts provider with maximum audio quality (uzbek)
python -m src.core.main --input_file video.mp4 --target_language uz --tts azure --audio_quality maximum --enhanced_segmentation --min_silence_duration 0.3 --max_segment_duration 20

# async assemblyai with speaker diarization and azure tts (russian)
python -m src.core.main --input_file video.mp4 --target_language ru --tts azure --audio_quality maximum

# incremental update (modify existing dubbing) (russian)
python -m src.core.main --input_file video.mp4 --target_language ru --update --output_directory data/outputs/existing_run/

# list all dubbing runs with cost information
python -m src.core.main --list_runs

# cost-optimized dubbing with budget enforcement
python -m src.core.main --input_file video.mp4 --target_language ru --daily_budget 20.0 --job_budget 5.0 --audio_quality standard

# enterprise dubbing with comprehensive cost tracking
python -m src.core.main --input_file video.mp4 --target_language es --daily_budget 100.0 --export_cost_data --cost_report
```

### config-first approach (recommended)

```bash
# comprehensive config file with all settings
python -m src.core.main --config run_config.json

# config with cli overrides for specific runs
python -m src.core.main --config run_config.json --target_language rus --original_subtitles

# production config with subtitles enabled
python -m src.core.main --config production_config.json
```

**benefits of config files:**

-   **comprehensive control**: all cli parameters available in config
-   **reproducible runs**: save exact settings for consistent results
-   **team collaboration**: share standardized configurations
-   **environment-specific**: different configs for dev/staging/production
-   **cost management**: centralized budget and pricing configuration

### supported languages

the system supports both 2-letter iso-639-1 and 3-letter iso-639-3 language codes for maximum compatibility. you can use either format:

**language code formats:**

-   **2-letter codes (recommended):** `uz`, `ru`, `en`
-   **3-letter codes (legacy support):** `uzb`, `rus`, `eng`

the platform currently supports three languages across all tts providers:

**supported languages (all providers):**

-   `uz`/`uzb` (uzbek)
-   `ru`/`rus` (russian)
-   `en`/`eng` (english)

**note:** both language code formats are fully supported. the system automatically handles conversion between formats internally.

### run management

the system automatically generates a unique uuid for each dubbing session and provides tools to manage runs:

```bash
# list all runs with status and metadata
python -m src.core.main --list_runs

# each run displays:
# - run id: 2292263e-3e65-4d45-acdd-6bd1c7faaf08
# - created: 2025-01-15t14:30:22
# - status: completed
# - input: video.mp4
# - languages: eng -> spa
# - files: 7 created
# - cost: $2.45 (estimated: $2.30)
```

**run tracking features:**

-   **automatic uuid generation**: each run gets a unique identifier
-   **status tracking**: running, completed, failed
-   **metadata storage**: input file, languages, creation time
-   **file inventory**: automatic tracking of all generated files
-   **cost tracking**: real-time cost monitoring and final cost summaries
-   **easy retrieval**: reference runs by uuid for updates or analysis

---

## 💰 cost tracking & budget management

the aizen platform includes a comprehensive cost tracking system that **monitors and analyzes** costs for all ai services used during dubbing. this is an **analytics-only system** that tracks spending without affecting business logic or processing decisions.

### key features

-   **real-time cost monitoring**: track costs as they occur during processing (analytics only)
-   **pre-execution cost estimation**: get accurate cost predictions before starting
-   **spending analysis**: analyze daily, weekly, monthly, and per-job spending patterns
-   **budget alerts**: optional notifications when spending approaches configured thresholds
-   **cost analytics**: detailed reporting, trend analysis, and optimization insights
-   **multi-provider tracking**: monitors assemblyai, gemini, tts providers, and gpu/cpu processing

> **important**: this is an analytics and monitoring system only. it does not control business logic, change processing behavior, or make decisions based on pricing. all cost tracking is for reporting and analysis purposes.

### quick start with cost tracking

```bash
# basic cost tracking with daily budget
python -m src.core.main --input_file video.mp4 --target_language es --daily_budget 25.0

# comprehensive budget management
python -m src.core.main --input_file video.mp4 --target_language es \
  --daily_budget 50.0 --weekly_budget 300.0 --job_budget 10.0

# cost estimation only (analytics preview, no processing)
python -m src.core.main --input_file video.mp4 --target_language es --cost_estimation

# with cost reporting and data export
python -m src.core.main --input_file video.mp4 --target_language es \
  --daily_budget 25.0 --cost_report --export_cost_data
```

### cost breakdown by provider

| provider       | pricing model | typical cost                       | what's tracked                          |
| -------------- | ------------- | ---------------------------------- | --------------------------------------- |
| **assemblyai** | per minute    | $0.37/min (best), $0.10/min (nano) | audio duration, model tier, features    |
| **gemini**     | per token     | $0.125/1k tokens                   | input/output tokens, model variant      |
| **azure tts**  | per character | $16/1m chars (neural)              | character count, voice type, quality    |
| **elevenlabs** | per character | $30/1m chars (premium)             | character count, voice tier             |
| **yandex tts** | per character | $15/1m chars                       | character count, voice type             |
| **gpu/cpu**    | per second    | $10/hour (gpu), $3/hour (cpu)      | processing time, operation type, device |

### cost monitoring examples

```bash
# development/testing with cost tracking
python -m src.core.main --input_file video.mp4 --target_language es \
  --daily_budget 10.0 --job_budget 5.0

# production with cost monitoring and alerts
python -m src.core.main --input_file video.mp4 --target_language es \
  --daily_budget 100.0 --weekly_budget 500.0 --monthly_budget 2000.0

# cost-optimized processing with analytics
python -m src.core.main --input_file video.mp4 --target_language es \
  --daily_budget 20.0 --audio_quality standard --tts yandex --cost_report
```

> **note**: budget parameters are used for monitoring and alerts only. they do not prevent or stop processing - they provide analytics and notifications about spending patterns.

### cost tracking in config files

```json
{
    "cost_tracking": {
        "enabled": true,
        "cost_estimation": true,
        "daily_budget_limit": 50.0,
        "weekly_budget_limit": 300.0,
        "monthly_budget_limit": 1000.0,
        "per_job_budget_limit": 25.0,
        "budget_enforcement": false,
        "alert_thresholds": [0.8, 0.9, 1.0],
        "detailed_reporting": true,
        "export_cost_data": true,
        "pricing_config_file": "data/custom_pricing.json"
    }
}
```

### default pricing configuration

the system uses `data/default_pricing_config.json` for default pricing. you can override specific providers by creating `data/pricing_config.json` or using `--pricing_config` with a custom file.

### cost optimization tips

-   **use nano model**: assemblyai nano costs 73% less than best model
-   **optimize audio quality**: standard quality reduces tts costs by ~40%
-   **batch processing**: process multiple videos together for efficiency
-   **choose providers wisely**: yandex tts is ~25% cheaper than azure neural
-   **monitor trends**: use cost analytics to identify optimization opportunities

see `docs/cost_tracking.md` for comprehensive documentation and `examples/` for configuration templates.

---

## ⚙️ configuration

### environment variables

create a `.env` file in the project root:

```env
# yandex speechkit
YANDEX_OAUTH_TOKEN=your_oauth_token
YANDEX_FOLDER_ID=your_folder_id
YANDEX_IAM_TOKEN=your_iam_token  # preferred method

# azure cognitive services
AZURE_SPEECH_KEY=your_speech_key
AZURE_SPEECH_REGION=your_region

# elevenlabs
ELEVENLABS_API_KEY=your_api_key

# assemblyai
ASSEMBLYAI_API_KEY=your_api_key

# google services
GOOGLE_API_KEY=your_gemini_api_key

# hugging face
HF_TOKEN=your_hf_token
```

### cli reference

| parameter                    | default         | description                                                                           |
| ---------------------------- | --------------- | ------------------------------------------------------------------------------------- |
| `--input_file`               | required        | path to input mp4 video                                                               |
| `--target_language`          | required        | target language code (2-letter: `uz`, `ru`, `en` or 3-letter: `uzb`, `rus`, `eng`)    |
| `--source_language`          | auto-detect     | source language code (2-letter: `uz`, `ru`, `en` or 3-letter: `uzb`, `rus`, `eng`)    |
| `--output_directory`         | `data/outputs/` | output directory path                                                                 |
| `--tts`                      | `yandex`        | tts provider (yandex/azure/elevenlabs)                                                |
| `--audio_quality`            | `maximum`       | audio quality preset (standard/high/maximum)                                          |
| `--stt`                      | `assemblyai`    | speech-to-text provider (async assemblyai with 2-5x speedup)                          |
| `--async_transcription`      | `true`          | use async assemblyai for enhanced performance (disable with --no-async_transcription) |
| `--device`                   | `cpu`           | processing device (cpu/cuda)                                                          |
| `--enhanced_segmentation`    | `true`          | natural pause detection                                                               |
| `--skip_diarization`         | `false`         | skip speaker identification                                                           |
| `--clean-intermediate-files` | `false`         | cleanup temp files                                                                    |
| `--dubbed_subtitles`         | `false`         | generate dubbed subtitles                                                             |
| `--original_subtitles`       | `false`         | generate original subtitles                                                           |
| `--update`                   | `false`         | incremental update mode                                                               |
| `--list_runs`                | `false`         | list all available dubbing runs                                                       |
| `--run_id`                   | auto-generate   | specify or reference a specific run uuid                                              |
| `--disable_cost_tracking`    | `false`         | disable cost tracking for ai services                                                 |
| `--cost_estimation`          | `true`          | enable pre-execution cost estimation                                                  |
| `--daily_budget`             | none            | daily spending limit in usd                                                           |
| `--weekly_budget`            | none            | weekly spending limit in usd                                                          |
| `--monthly_budget`           | none            | monthly spending limit in usd                                                         |
| `--job_budget`               | none            | per job spending limit in usd                                                         |
| `--budget_alerts`            | `false`         | enable budget alert notifications (analytics only, no enforcement)                    |
| `--pricing_config`           | none            | path to custom pricing configuration file                                             |
| `--export_cost_data`         | `false`         | automatically export cost data after job completion                                   |
| `--cost_report`              | `false`         | generate and display cost report after job completion                                 |

<details>
<summary>view all cli options</summary>

```bash
python -m src.core.main --help
```

see `src/core/command_line.py` for complete parameter documentation.

</details>

### additional configuration fields

| section                                  | key                                                           | description                                                                    | default          |
| ---------------------------------------- | ------------------------------------------------------------- | ------------------------------------------------------------------------------ | ---------------- |
| input.separation                         | model                                                         | demucs model name to use for source separation                                 | `htdemucs_ft`    |
| input.separation                         | segment_duration                                              | chunk length in seconds for demucs `--segment`                                 | `30`             |
| translation.analyzer / segment_processor | model                                                         | specific model identifier for provider (e.g. `gemini-2.5-flash-preview-05-20`) | provider default |
| models.tts_provider                      | provider used for tts (`yandex`, `azure`, `elevenlabs`)       | `yandex`                                                                       |
| models.audio_quality                     | audio quality preset (`standard`, `high`, `maximum`)          | `maximum`                                                                      |
| models.tts_voice                         | preferred voice id / name applied to all speakers (optional)  | _none_                                                                         |
| performance.rate_limit_per_sec           | global api call throttle (0 = unlimited)                      | `2`                                                                            |
| performance.translation_workers          | max parallel workers for gemini translation                   | `20`                                                                           |
| output.video_encoder                     | ffmpeg encoder to use (`h264`, `libx264`, `h264_nvenc`, etc.) | `libx264`                                                                      |

---

## 🏗️ architecture

### processing pipeline

```mermaid
graph TD
    A[input video] --> B[audio/video separation]
    B --> C[vocal isolation - demucs]
    C --> D[speaker diarization - pyannote]
    D --> E[speech segmentation]
    E --> F[speech-to-text - async assemblyai]
    F --> G[translation - gemini]
    G --> H[voice assignment]
    H --> I[text-to-speech]
    I --> J[audio merging]
    J --> K[subtitle generation]
    K --> L[final video assembly]
    L --> M[cloud backup - optional]
```

### project structure

```
aizen_platform/server/
├── src/
│   ├── core/              # main orchestration
│   │   ├── main.py        # entry point
│   │   ├── dubbing.py     # dubbing orchestrator
│   │   └── command_line.py # cli parser
│   ├── audio_processing/  # audio manipulation
│   │   ├── demucs/        # vocal isolation
│   │   ├── assemblyai/    # speech-to-text
│   │   └── tts/           # text-to-speech providers
│   ├── video_processing/  # video operations
│   │   ├── ffmpeg.py      # ffmpeg wrapper
│   │   └── subtitles.py   # subtitle generation
│   ├── translation/       # translation engines
│   ├── cost_tracking/     # comprehensive cost management
│   │   ├── models.py      # cost data models
│   │   ├── calculators.py # provider-specific cost calculators
│   │   ├── middleware.py  # cost tracking middleware
│   │   ├── budget.py      # budget management and alerts
│   │   ├── analytics.py   # cost analytics and reporting
│   │   ├── estimator.py   # pre-execution cost estimation
│   │   └── integration.py # main cost tracking integration
│   ├── api/              # cloud service wrappers
│   └── utils/            # shared utilities
│       └── run_manager.py # uuid-based run management
├── data/
│   ├── inputs/           # input videos (optional)
│   └── outputs/          # processing results
├── scripts/              # utility scripts
└── pyproject.toml        # dependencies
```

### output structure

each dubbing run creates a uuid-based directory for complete isolation:

```
data/outputs/
├── runs_registry.json                    # run tracking registry
├── 2292263e-3e65-4d45-acdd-6bd1c7faaf08/ # run 1 (uuid directory)
│   ├── dubbed_audio_spa.mp3              # final dubbed audio
│   ├── dubbed_vocals.mp3                 # vocals track
│   ├── spa.srt                          # dubbed subtitles
│   ├── eng.srt                          # original subtitles
│   ├── utterance_metadata.json          # processing metadata
│   ├── video_audio.wav                  # extracted audio
│   ├── video_video.mp4                  # video without audio
│   ├── transcription.txt                # debug transcription
│   ├── cost_summary.json                # final cost breakdown
│   └── cost_report.json                 # detailed cost analytics
├── a1b2c3d4-e5f6-4789-90ab-123456789012/ # run 2 (different uuid)
│   ├── dubbed_audio_rus.mp3
│   ├── dubbed_vocals.mp3
│   ├── rus.srt
│   ├── eng.srt
│   └── utterance_metadata.json
└── ...                                  # additional runs
```

**benefits of uuid organization:**

-   **complete isolation**: each run has its own directory
-   **easy identification**: runs identified by unique uuid
-   **no file conflicts**: multiple runs can process simultaneously
-   **run tracking**: comprehensive registry with metadata and status
-   **easy cleanup**: remove specific runs without affecting others

---

## 🛠️ development

### setup development environment

```bash
# clone repository
git clone <repository-url>
cd aizen_platform/server

# setup python environment
uv venv --python=3.11.11
uv sync --dev

# install pre-commit hooks
pre-commit install
```

### code quality

```bash
# format code
black src/
ruff check src/ --fix

# type checking
mypy src/

# pre-commit checks
pre-commit run --all-files
```

### adding new tts providers

1. create new provider class in `src/audio_processing/text_to_speech_<provider>.py`
2. implement required interface methods
3. add provider to `src/core/main.py` tts selection
4. update cli options and documentation

---

## 🤝 contributing

we welcome contributions! please follow these guidelines:

### commit conventions

use conventional commits for clear history:

```bash
feat: add new tts provider support
fix: resolve audio sync issues
docs: update installation guide
refactor: improve error handling
test: add integration tests
```

### code style

-   follow pep 8 guidelines
-   use lowercase for comments and logging
-   line length ≤ 100 characters
-   add type hints for new functions
-   write docstrings for public methods

### pull request process

1. fork the repository
2. create feature branch: `git checkout -b feature/amazing-feature`
3. commit changes: `git commit -m 'feat: add amazing feature'`
4. push to branch: `git push origin feature/amazing-feature`
5. open pull request with detailed description

---

## 📋 roadmap

### upcoming features

-   [x] **uuid organization**: unique run identification and file isolation ✅
-   [x] **comprehensive config support**: all cli parameters available in config files ✅
-   [x] **parallel translation**: gemini translation with up to 20 parallel workers ✅
-   [x] **async assemblyai**: 2-5x faster transcription with parallel processing ✅
-   [x] **comprehensive cost tracking**: real-time monitoring and analytics (analytics-only) ✅
-   [x] **cost estimation**: pre-execution cost predictions and optimization insights ✅
-   [x] **cost analytics**: detailed reporting, trend analysis, and spending insights ✅
-   [ ] **web interface**: streamlit-based gui for easier usage
-   [ ] **batch processing**: multiple video processing support
-   [ ] **voice cloning**: custom voice training capabilities
-   [ ] **real-time dubbing**: live video stream processing
-   [ ] **quality metrics**: automated dubbing quality assessment

### technical improvements

-   [ ] **ci/cd**: github actions workflow
-   [ ] **docker**: containerized deployment
-   [ ] **api**: rest api for programmatic access
-   [ ] **monitoring**: performance and error tracking

---

## 📄 license

this project currently has no license. please add an appropriate open source license before public distribution.

---

## 🆘 support

### troubleshooting

**common issues:**

1. **ffmpeg not found**: ensure ffmpeg is installed and in system path
2. **cuda errors**: verify nvidia drivers and cuda toolkit installation
3. **api key errors**: check environment variable configuration
4. **memory issues**: reduce batch size or use cpu processing

**getting help:**

-   check existing issues in the repository
-   review logs in `data/outputs/<run>/` for detailed error information
-   ensure all prerequisites are properly installed

### performance optimization

#### async assemblyai performance

the platform now uses **async assemblyai transcription by default**, providing massive performance improvements:

| metric             | before (sync)      | after (async)     | improvement               |
| ------------------ | ------------------ | ----------------- | ------------------------- |
| transcription time | 10-20s per minute  | 1-3s total        | **5-10x faster**          |
| overall dubbing    | 65-155s per minute | 25-45s per minute | **2.5-3.5x faster**       |
| api efficiency     | sequential calls   | parallel batching | **+300% throughput**      |
| resource usage     | single-threaded    | multi-threaded    | **+200% cpu utilization** |

**async features:**

-   **parallel processing**: multiple audio chunks transcribed simultaneously
-   **intelligent batching**: optimized api usage and rate limiting
-   **progress tracking**: real-time updates and eta calculation
-   **fault tolerance**: graceful error handling and recovery

**for faster processing:**

-   use gpu acceleration with `--device cuda`
-   async assemblyai provides 2-5x faster transcription (enabled by default)
-   enable intermediate file cleanup
-   use enhanced segmentation for better accuracy
-   increase `translation_workers` for faster gemini translation (up to 20 parallel workers)
-   adjust `max_concurrent_transcriptions` for optimal assemblyai throughput

**for cost optimization:**

-   use assemblyai nano model for 73% cost reduction
-   choose standard audio quality for ~40% tts savings
-   select cost-effective providers (yandex vs azure/elevenlabs)
-   set appropriate budget limits to control spending
-   monitor cost trends with built-in analytics

**for better quality:**

-   async assemblyai automatically uses best available models with advanced features
-   enable speaker diarization for better speaker identification
-   adjust segmentation parameters for optimal chunk boundaries
-   use `--audio_quality maximum` for highest quality tts output (48khz, 192kbps+)
-   choose premium tts providers (azure, elevenlabs) for higher quality voices

---

## 🎵 audio quality settings

the platform supports three audio quality presets to balance quality vs file size:

### quality presets

| preset     | sample rate | bitrate     | description                                    | use case                  |
| ---------- | ----------- | ----------- | ---------------------------------------------- | ------------------------- |
| `standard` | 16khz       | 128-192kbps | good quality, smaller files, faster processing | testing, quick previews   |
| `high`     | 24khz       | 160-256kbps | balanced quality and file size                 | most production use cases |
| `maximum`  | 48khz       | 192-320kbps | highest quality, larger files                  | premium content, archival |

### provider-specific formats

| provider       | standard                         | high                             | maximum                          |
| -------------- | -------------------------------- | -------------------------------- | -------------------------------- |
| **azure**      | audio-16khz-128kbitrate-mono-mp3 | audio-24khz-160kbitrate-mono-mp3 | audio-48khz-192kbitrate-mono-mp3 |
| **elevenlabs** | mp3_44100_128                    | mp3_44100_160                    | mp3_44100_192                    |
| **yandex**     | wav (high quality by default)    | wav (high quality by default)    | wav (high quality by default)    |

### usage examples

```bash
# maximum quality (default)
python -m src.core.main --input_file video.mp4 --target_language uz --audio_quality maximum

# balanced quality/size
python -m src.core.main --input_file video.mp4 --target_language uz --audio_quality high

# faster processing
python -m src.core.main --input_file video.mp4 --target_language uz --audio_quality standard
```

**note:** maximum quality produces audio that matches tts provider playgrounds and eliminates the "compressed mp3" sound quality issues.

---

## 📋 unified run configuration

the platform supports comprehensive json configuration files that can control **all cli parameters** through a single config file.

### basic usage

```bash
# use config file with all settings
python -m src.core.main --config run_config.json

# config + cli overrides (cli takes precedence)
python -m src.core.main --config run_config.json --target_language rus --original_subtitles
```

### comprehensive parameter coverage

**all cli parameters** are now available in config format:

| cli parameter                | config location                    | description                          |
| ---------------------------- | ---------------------------------- | ------------------------------------ |
| `--original_subtitles`       | `output.original_subtitles`        | generate original language subtitles |
| `--dubbed_subtitles`         | `output.dubbed_subtitles`          | generate dubbed language subtitles   |
| `--skip_diarization`         | `processing.skip_diarization`      | skip speaker identification          |
| `--enhanced_segmentation`    | `processing.enhanced_segmentation` | natural pause detection              |
| `--max_segment_duration`     | `processing.max_segment_duration`  | maximum speech segment length        |
| `--clean_intermediate_files` | `output.clean_intermediate_files`  | cleanup temp files                   |
| `--target_language_region`   | `models.target_language_region`    | language region/accent               |
| `--async_transcription`      | `processing.async_transcription`   | use async assemblyai (2-5x speedup)  |
| `--update`                   | `processing.update_mode`           | incremental update mode              |
| `--run_id`                   | `processing.run_id`                | specific run identifier              |

### config structure

```json
{
  "input": { "source_path": "video.mp4", "silence": {...}, "separation": {...} },
  "translation": { "source_lang": "eng", "target_lang": "uzb", "analyzer": {...} },
  "processing": { "skip_diarization": false, "enhanced_segmentation": true, "async_transcription": true },
  "performance": { "max_workers": 10, "translation_workers": 20, "use_gpu": true },
  "models": { "tts_provider": "azure", "audio_quality": "maximum", "target_language_region": "uz" },
  "output": { "original_subtitles": true, "dubbed_subtitles": true },
  "cost_tracking": { "enabled": true, "daily_budget_limit": 50.0, "budget_enforcement": true }
}
```

see `run_config.json` for complete example with all available fields and detailed comments.

---

<div align="center">

**made with ❤️ by the aizen platform team**

</div>
