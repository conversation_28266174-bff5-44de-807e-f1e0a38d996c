{"assemblyai": {"provider": "assemblyai", "pricing_model": "per_minute", "base_cost": "0.37", "currency": "USD", "tier_multipliers": {"best": "1.0", "nano": "0.27", "universal": "0.54"}, "metadata": {"features": {"speaker_labels": "0.10", "sentiment_analysis": "0.05", "entity_detection": "0.05", "auto_highlights": "0.03"}, "rate_limit_rpm": 1000, "description": "assemblyai speech-to-text pricing as of 2024"}}, "gemini": {"provider": "gemini", "pricing_model": "per_token", "base_cost": "0.000125", "currency": "USD", "tier_multipliers": {"gemini-pro": "1.0", "gemini-pro-vision": "1.5", "gemini-ultra": "3.0", "gemini-flash": "0.4"}, "metadata": {"output_token_multiplier": "2.5", "rate_limit_rpm": 1000, "description": "google gemini translation pricing as of 2024"}}, "azure_tts": {"provider": "azure_tts", "pricing_model": "per_character", "base_cost": "0.000016", "currency": "USD", "tier_multipliers": {"standard": "1.0", "neural": "2.0", "premium": "3.0"}, "metadata": {"quality_multipliers": {"standard": "1.0", "high": "1.2", "maximum": "1.5"}, "description": "azure cognitive services tts pricing as of 2024"}}, "elevenlabs_tts": {"provider": "elevenlabs_tts", "pricing_model": "per_character", "base_cost": "0.00003", "currency": "USD", "tier_multipliers": {"standard": "1.0", "premium": "2.0", "custom": "5.0"}, "volume_discounts": {"1000000": "0.1", "5000000": "0.2"}, "metadata": {"voice_cloning_cost": "11.0", "description": "elevenlabs tts pricing as of 2024"}}, "yandex_tts": {"provider": "yandex_tts", "pricing_model": "per_character", "base_cost": "0.000015", "currency": "USD", "tier_multipliers": {"standard": "1.0", "premium": "1.5"}, "metadata": {"language_multipliers": {"ru": "1.0", "en": "1.0", "other": "1.2"}, "description": "yandex speechkit tts pricing as of 2024"}}, "gpu_processing": {"provider": "gpu_processing", "pricing_model": "per_second", "base_cost": "0.0028", "currency": "USD", "tier_multipliers": {"demucs": "1.5", "diarization": "1.2", "video_processing": "1.0"}, "metadata": {"device_types": {"rtx_4090": "1.0", "a100": "2.5", "h100": "4.0"}, "hourly_rate": "10.08", "description": "estimated cloud gpu processing costs as of 2024"}}, "cpu_processing": {"provider": "cpu_processing", "pricing_model": "per_second", "base_cost": "0.0008", "currency": "USD", "tier_multipliers": {"audio_processing": "0.8", "video_processing": "1.0", "general": "0.6"}, "metadata": {"cpu_types": {"intel_i9": "1.0", "amd_ryzen": "0.9", "cloud_cpu": "1.2"}, "hourly_rate": "2.88", "description": "estimated cloud cpu processing costs as of 2024"}}}