"""comprehensive tests for cost tracking system.

this module contains unit tests, integration tests, and performance tests
for all cost tracking components.
"""

import pytest
import time
from decimal import Decimal
from unittest.mock import Mock, patch

from src.cost_tracking.models import (
    CostEntry,
    CostBreakdown,
    ProviderType,
    CostType,
    PricingConfig,
    PricingModel,
    BudgetConfig,
    CostEstimate,
)
from src.cost_tracking.calculators import (
    AssemblyAICostCalculator,
    GeminiCostCalculator,
    TTSCostCalculator,
    ProcessingCostCalculator,
)
from src.cost_tracking.middleware import CostTracker, create_cost_tracker
from src.cost_tracking.budget import BudgetManager, BudgetAlert, AlertLevel
from src.cost_tracking.analytics import CostAnalytics
from src.cost_tracking.estimator import CostEstimator
from src.cost_tracking.pricing_config import PricingConfigManager


class TestCostModels:
    """test cost tracking data models."""
    
    def test_cost_entry_creation(self):
        """test cost entry creation and serialization."""
        entry = CostEntry(
            provider=ProviderType.ASSEMBLYAI,
            cost_type=CostType.AUDIO_DURATION,
            amount=Decimal("1.50"),
            unit_cost=Decimal("0.37"),
            units_consumed=4.05,
            metadata={"model": "best"}
        )
        
        assert entry.provider == ProviderType.ASSEMBLYAI
        assert entry.cost_type == CostType.AUDIO_DURATION
        assert entry.amount == Decimal("1.50")
        assert entry.units_consumed == 4.05
        
        # test serialization
        entry_dict = entry.to_dict()
        assert entry_dict["provider"] == "assemblyai"
        assert entry_dict["cost_type"] == "audio_duration"
        assert entry_dict["amount"] == 1.50
        
        # test deserialization
        restored_entry = CostEntry.from_dict(entry_dict)
        assert restored_entry.provider == entry.provider
        assert restored_entry.amount == entry.amount
    
    def test_cost_breakdown_aggregation(self):
        """test cost breakdown aggregation functionality."""
        breakdown = CostBreakdown()
        
        # add assemblyai cost
        assemblyai_entry = CostEntry(
            provider=ProviderType.ASSEMBLYAI,
            cost_type=CostType.AUDIO_DURATION,
            amount=Decimal("1.50"),
            unit_cost=Decimal("0.37"),
            units_consumed=4.05,
        )
        breakdown.add_cost_entry(assemblyai_entry)
        
        # add gemini cost
        gemini_entry = CostEntry(
            provider=ProviderType.GEMINI,
            cost_type=CostType.TOKEN_USAGE,
            amount=Decimal("0.25"),
            unit_cost=Decimal("0.000125"),
            units_consumed=2000,
        )
        breakdown.add_cost_entry(gemini_entry)
        
        assert len(breakdown.assemblyai_costs) == 1
        assert len(breakdown.gemini_costs) == 1
        assert breakdown.total_cost == Decimal("1.75")
        
        # test provider totals
        assert breakdown.get_provider_total(ProviderType.ASSEMBLYAI) == Decimal("1.50")
        assert breakdown.get_provider_total(ProviderType.GEMINI) == Decimal("0.25")


class TestCostCalculators:
    """test cost calculators for different providers."""
    
    def test_assemblyai_calculator(self):
        """test assemblyai cost calculation."""
        pricing_config = PricingConfig(
            provider=ProviderType.ASSEMBLYAI,
            pricing_model=PricingModel.PER_MINUTE,
            base_cost=Decimal("0.37"),
        )
        
        calculator = AssemblyAICostCalculator(pricing_config)
        
        # test basic calculation
        cost_entry = calculator.calculate_cost(
            audio_duration_minutes=5.0,
            model_name="best",
            features=["speaker_labels"]
        )
        
        assert cost_entry.provider == ProviderType.ASSEMBLYAI
        assert cost_entry.cost_type == CostType.AUDIO_DURATION
        assert cost_entry.units_consumed == 5.0
        assert cost_entry.amount > Decimal("0")
        assert "model_name" in cost_entry.metadata
    
    def test_gemini_calculator(self):
        """test gemini cost calculation."""
        pricing_config = PricingConfig(
            provider=ProviderType.GEMINI,
            pricing_model=PricingModel.PER_TOKEN,
            base_cost=Decimal("0.000125"),
        )
        
        calculator = GeminiCostCalculator(pricing_config)
        
        # test token-based calculation
        cost_entry = calculator.calculate_cost(
            input_tokens=1000,
            output_tokens=800,
            model_name="gemini-pro"
        )
        
        assert cost_entry.provider == ProviderType.GEMINI
        assert cost_entry.cost_type == CostType.TOKEN_USAGE
        assert cost_entry.units_consumed == 1800  # total tokens
        assert cost_entry.amount > Decimal("0")
        assert cost_entry.metadata["input_tokens"] == 1000
        assert cost_entry.metadata["output_tokens"] == 800
    
    def test_tts_calculator(self):
        """test tts cost calculation."""
        pricing_config = PricingConfig(
            provider=ProviderType.AZURE_TTS,
            pricing_model=PricingModel.PER_CHARACTER,
            base_cost=Decimal("0.000016"),
        )
        
        calculator = TTSCostCalculator(pricing_config)
        
        # test character-based calculation
        cost_entry = calculator.calculate_cost(
            character_count=1000,
            voice_type="neural",
            audio_quality="high",
            language="en"
        )
        
        assert cost_entry.provider == ProviderType.AZURE_TTS
        assert cost_entry.cost_type == CostType.CHARACTER_COUNT
        assert cost_entry.units_consumed == 1000
        assert cost_entry.amount > Decimal("0")
        assert cost_entry.metadata["voice_type"] == "neural"
    
    def test_processing_calculator(self):
        """test processing cost calculation."""
        pricing_config = PricingConfig(
            provider=ProviderType.GPU_PROCESSING,
            pricing_model=PricingModel.PER_SECOND,
            base_cost=Decimal("0.0028"),
        )
        
        calculator = ProcessingCostCalculator(pricing_config)
        
        # test time-based calculation
        cost_entry = calculator.calculate_cost(
            processing_time_seconds=120.0,
            operation_type="demucs",
            device_type="gpu"
        )
        
        assert cost_entry.provider == ProviderType.GPU_PROCESSING
        assert cost_entry.cost_type == CostType.PROCESSING_TIME
        assert cost_entry.units_consumed == 120.0
        assert cost_entry.amount > Decimal("0")
        assert cost_entry.metadata["operation_type"] == "demucs"


class TestCostTracker:
    """test cost tracking middleware."""
    
    def test_cost_tracker_creation(self):
        """test cost tracker creation and basic functionality."""
        run_id = "test_run_123"
        tracker = create_cost_tracker(run_id)
        
        assert tracker.run_id == run_id
        assert tracker.get_total_cost() == 0.0
        
        # test adding cost entry
        cost_entry = CostEntry(
            provider=ProviderType.ASSEMBLYAI,
            cost_type=CostType.AUDIO_DURATION,
            amount=Decimal("1.50"),
            unit_cost=Decimal("0.37"),
            units_consumed=4.05,
        )
        
        tracker.add_cost_entry(cost_entry)
        assert tracker.get_total_cost() == 1.50
        assert tracker.get_provider_cost(ProviderType.ASSEMBLYAI) == 1.50
    
    def test_cost_tracker_thread_safety(self):
        """test cost tracker thread safety."""
        import threading
        
        run_id = "test_thread_safety"
        tracker = create_cost_tracker(run_id)
        
        def add_costs():
            for i in range(100):
                cost_entry = CostEntry(
                    provider=ProviderType.ASSEMBLYAI,
                    cost_type=CostType.AUDIO_DURATION,
                    amount=Decimal("0.01"),
                    unit_cost=Decimal("0.01"),
                    units_consumed=1.0,
                )
                tracker.add_cost_entry(cost_entry)
        
        # run multiple threads adding costs
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=add_costs)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # should have 500 entries totaling $5.00
        assert tracker.get_total_cost() == 5.00


class TestBudgetManager:
    """test budget management functionality."""
    
    def test_budget_tracking(self):
        """test budget tracking and alerts."""
        budget_config = BudgetConfig(
            daily_limit=Decimal("10.00"),
            alert_thresholds=[0.8, 0.9, 1.0],
            enforcement_enabled=True,
        )
        
        budget_manager = BudgetManager(budget_config)
        
        # test cost tracking
        cost_entry = CostEntry(
            provider=ProviderType.ASSEMBLYAI,
            cost_type=CostType.AUDIO_DURATION,
            amount=Decimal("8.50"),  # 85% of daily limit
            unit_cost=Decimal("0.37"),
            units_consumed=23.0,
        )
        
        alerts = budget_manager.track_cost(cost_entry, "test_run")
        
        # should generate alert for 80% threshold
        assert len(alerts) > 0
        assert alerts[0].alert_level in [AlertLevel.WARNING, AlertLevel.CRITICAL]
        assert alerts[0].budget_type == "daily"
    
    def test_budget_enforcement(self):
        """test budget enforcement functionality."""
        budget_config = BudgetConfig(
            daily_limit=Decimal("5.00"),
            enforcement_enabled=True,
        )
        
        budget_manager = BudgetManager(budget_config)
        
        # test pre-operation check
        can_proceed, alerts = budget_manager.check_budget_before_operation(
            Decimal("6.00"), "test_run"
        )
        
        assert not can_proceed  # should not allow operation
        assert len(alerts) > 0
        assert alerts[0].alert_level == AlertLevel.EXCEEDED


class TestCostAnalytics:
    """test cost analytics and reporting."""
    
    def test_cost_report_generation(self):
        """test cost report generation."""
        analytics = CostAnalytics()
        
        # create sample cost entries
        entries = [
            CostEntry(
                provider=ProviderType.ASSEMBLYAI,
                cost_type=CostType.AUDIO_DURATION,
                amount=Decimal("1.50"),
                unit_cost=Decimal("0.37"),
                units_consumed=4.05,
                timestamp=time.time(),
            ),
            CostEntry(
                provider=ProviderType.GEMINI,
                cost_type=CostType.TOKEN_USAGE,
                amount=Decimal("0.25"),
                unit_cost=Decimal("0.000125"),
                units_consumed=2000,
                timestamp=time.time(),
            ),
        ]
        
        report = analytics.generate_report(entries)
        
        assert report.total_cost == Decimal("1.75")
        assert "assemblyai" in report.cost_by_provider
        assert "gemini" in report.cost_by_provider
        assert report.job_count >= 1
    
    def test_trend_analysis(self):
        """test cost trend analysis."""
        analytics = CostAnalytics()
        
        # create sample entries with timestamps
        entries = []
        base_time = time.time() - (7 * 24 * 3600)  # 7 days ago
        
        for i in range(7):
            entry = CostEntry(
                provider=ProviderType.ASSEMBLYAI,
                cost_type=CostType.AUDIO_DURATION,
                amount=Decimal(str(1.0 + i * 0.1)),  # increasing cost
                unit_cost=Decimal("0.37"),
                units_consumed=float(1.0 + i * 0.1),
                timestamp=base_time + (i * 24 * 3600),
            )
            entries.append(entry)
        
        trends = analytics.analyze_cost_trends(entries, days=7)
        
        assert "trend_direction" in trends
        assert "total_cost" in trends
        assert trends["trend_direction"] in ["increasing", "decreasing", "stable"]


class TestCostEstimator:
    """test cost estimation functionality."""
    
    @patch('subprocess.run')
    def test_cost_estimation(self, mock_subprocess):
        """test job cost estimation."""
        # mock ffprobe output for duration
        mock_subprocess.return_value.returncode = 0
        mock_subprocess.return_value.stdout = "300.0"  # 5 minutes
        
        estimator = CostEstimator()
        
        estimate = estimator.estimate_job_cost(
            input_file="test_video.mp4",
            target_languages=["es", "fr"],
            tts_provider="azure",
        )
        
        assert estimate.estimated_audio_duration_minutes == 5.0
        assert len(estimate.target_languages) == 2
        assert estimate.total_estimated_cost > Decimal("0")
        assert estimate.estimated_assemblyai_cost > Decimal("0")
        assert estimate.estimated_gemini_cost > Decimal("0")
        assert estimate.estimated_tts_cost > Decimal("0")


class TestPerformance:
    """performance tests for cost tracking."""
    
    def test_cost_calculation_performance(self):
        """test that cost calculations don't add significant latency."""
        pricing_config = PricingConfig(
            provider=ProviderType.ASSEMBLYAI,
            pricing_model=PricingModel.PER_MINUTE,
            base_cost=Decimal("0.37"),
        )
        
        calculator = AssemblyAICostCalculator(pricing_config)
        
        # measure time for 1000 calculations
        start_time = time.time()
        
        for _ in range(1000):
            calculator.calculate_cost(
                audio_duration_minutes=5.0,
                model_name="best",
                features=["speaker_labels"]
            )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # should complete 1000 calculations in under 1 second
        assert total_time < 1.0
        
        # average time per calculation should be under 1ms
        avg_time = total_time / 1000
        assert avg_time < 0.001
    
    def test_cost_tracker_performance(self):
        """test cost tracker performance with many entries."""
        tracker = create_cost_tracker("performance_test")
        
        start_time = time.time()
        
        # add 10000 cost entries
        for i in range(10000):
            cost_entry = CostEntry(
                provider=ProviderType.ASSEMBLYAI,
                cost_type=CostType.AUDIO_DURATION,
                amount=Decimal("0.01"),
                unit_cost=Decimal("0.01"),
                units_consumed=1.0,
            )
            tracker.add_cost_entry(cost_entry)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # should handle 10000 entries in under 1 second
        assert total_time < 1.0
        assert tracker.get_total_cost() == 100.0  # 10000 * 0.01


if __name__ == "__main__":
    pytest.main([__file__])
