"""integration tests for cost tracking in the full dubbing pipeline.

this module tests cost tracking integration with the actual dubbing components
to ensure costs are properly tracked throughout the entire process.
"""

import pytest
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from decimal import Decimal

from src.config import RunConfig, InputProcessingConfig, TranslationConfig, CostTrackingConfig
from src.cost_tracking.integration import CostTrackingIntegration
from src.cost_tracking.middleware import get_cost_tracker, create_cost_tracker
from src.cost_tracking.models import ProviderType
from src.audio_processing.speech_to_text_assemblyai import SpeechToTextAssemblyAI
from src.translation.translation_gemini import TranslationGemini
from src.audio_processing.text_to_speech_azure import TextToSpeechAzure


class TestCostTrackingIntegration:
    """test cost tracking integration with the dubbing pipeline."""
    
    def setup_method(self):
        """setup test configuration."""
        self.run_id = "test_integration_run"
        
        # create test configuration
        self.config = RunConfig(
            input=InputProcessingConfig(
                source_path="test_video.mp4",
                source_lang="en",
            ),
            translation=TranslationConfig(
                target_lang="es",
                provider="gemini",
            ),
            cost_tracking=CostTrackingConfig(
                enabled=True,
                cost_estimation=True,
                daily_budget_limit=50.0,
                budget_enforcement=True,
            )
        )
        
        # create cost integration
        self.cost_integration = CostTrackingIntegration(self.config, self.run_id)
    
    def teardown_method(self):
        """cleanup after tests."""
        # cleanup cost tracker
        from src.cost_tracking.middleware import remove_cost_tracker
        remove_cost_tracker(self.run_id)
    
    @patch('subprocess.run')
    def test_cost_estimation_integration(self, mock_subprocess):
        """test cost estimation integration."""
        # mock ffprobe output
        mock_subprocess.return_value.returncode = 0
        mock_subprocess.return_value.stdout = "300.0"  # 5 minutes
        
        # test cost estimation
        estimate = self.cost_integration.estimate_job_cost(
            input_file="test_video.mp4",
            target_languages=["es"],
            tts_provider="azure",
        )
        
        assert estimate is not None
        assert estimate["total_estimated_cost"] > 0
        assert estimate["estimated_assemblyai_cost"] > 0
        assert estimate["estimated_gemini_cost"] > 0
        assert estimate["estimated_tts_cost"] > 0
    
    def test_assemblyai_cost_tracking_integration(self):
        """test assemblyai cost tracking integration."""
        # create mock assemblyai instance
        with patch.dict(os.environ, {"assemblyai_api_key": "test_key"}):
            stt = SpeechToTextAssemblyAI()
            
            # ensure cost calculator is initialized
            assert stt.cost_calculator is not None
            
            # create cost tracker
            tracker = create_cost_tracker(self.run_id)
            
            # mock the transcription to avoid actual api calls
            with patch.object(stt, '_transcribe', return_value="test transcription"):
                # test transcription with cost tracking
                utterance_metadata = [
                    {"path": "test_audio.wav", "start": 0.0, "end": 5.0}
                ]
                
                result = stt.transcribe_audio_chunks(
                    utterance_metadata=utterance_metadata,
                    source_language="en",
                    no_dubbing_phrases=[],
                    run_id=self.run_id,
                )
                
                # verify cost was tracked
                assert tracker.get_total_cost() > 0
                assert tracker.get_provider_cost(ProviderType.ASSEMBLYAI) > 0
    
    def test_gemini_cost_tracking_integration(self):
        """test gemini translation cost tracking integration."""
        # create mock gemini instance
        with patch.dict(os.environ, {"google_api_key": "test_key"}):
            translator = TranslationGemini()
            
            # ensure cost calculator is initialized
            assert translator.cost_calculator is not None
            
            # create cost tracker
            tracker = create_cost_tracker(self.run_id)
            
            # mock the translation to avoid actual api calls
            with patch.object(translator, '_translate_text_chunk', return_value="texto traducido"):
                # test translation with cost tracking
                utterance_metadata = [
                    {"text": "hello world", "start": 0.0, "end": 2.0}
                ]
                
                result = translator.translate_utterances(
                    utterance_metadata=utterance_metadata,
                    source_language="en",
                    target_language="es",
                    run_id=self.run_id,
                )
                
                # verify cost was tracked
                assert tracker.get_total_cost() > 0
                assert tracker.get_provider_cost(ProviderType.GEMINI) > 0
    
    def test_tts_cost_tracking_integration(self):
        """test tts cost tracking integration."""
        # create mock azure tts instance
        with patch.dict(os.environ, {
            "azure_speech_key": "test_key",
            "azure_speech_region": "test_region"
        }):
            tts = TextToSpeechAzure()
            
            # ensure cost calculator is initialized
            assert tts.cost_calculator is not None
            
            # create cost tracker
            tracker = create_cost_tracker(self.run_id)
            
            # mock the synthesis to avoid actual api calls
            with patch.object(tts, '_synthesize_chunk', return_value=b"fake_audio_data"):
                with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                    output_file = temp_file.name
                
                try:
                    # test synthesis with cost tracking
                    result = tts._convert_text_to_speech_primary(
                        assigned_voice="en-US-AriaNeural",
                        target_language="en",
                        output_filename=output_file,
                        text="hello world",
                        speed=1.0,
                        run_id=self.run_id,
                    )
                    
                    # verify cost was tracked
                    assert tracker.get_total_cost() > 0
                    assert tracker.get_provider_cost(ProviderType.AZURE_TTS) > 0
                    
                finally:
                    # cleanup temp file
                    if os.path.exists(output_file):
                        os.unlink(output_file)
    
    def test_processing_cost_tracking_integration(self):
        """test gpu/cpu processing cost tracking integration."""
        from src.cost_tracking.processing_tracker import ProcessingTimeTracker
        
        # create cost tracker
        tracker = create_cost_tracker(self.run_id)
        
        # test demucs cost tracking
        demucs_tracker = ProcessingTimeTracker("demucs", "gpu")
        demucs_tracker.start()
        
        # simulate processing time
        import time
        time.sleep(0.1)  # 100ms
        
        processing_time = demucs_tracker.stop(self.run_id)
        
        # verify cost was tracked
        assert processing_time > 0
        assert tracker.get_total_cost() > 0
        assert tracker.get_provider_cost(ProviderType.GPU_PROCESSING) > 0
    
    def test_budget_enforcement_integration(self):
        """test budget enforcement in the integration."""
        # set very low budget limit
        self.config.cost_tracking.daily_budget_limit = 0.01
        
        # recreate integration with low budget
        integration = CostTrackingIntegration(self.config, "budget_test_run")
        
        # test cost estimation that should exceed budget
        with patch('subprocess.run') as mock_subprocess:
            mock_subprocess.return_value.returncode = 0
            mock_subprocess.return_value.stdout = "3600.0"  # 1 hour of audio
            
            estimate = integration.estimate_job_cost(
                input_file="long_video.mp4",
                target_languages=["es", "fr", "de"],  # multiple languages
                tts_provider="azure",
            )
            
            # should return none due to budget enforcement
            assert estimate is None
    
    def test_cost_finalization_integration(self):
        """test cost finalization and reporting."""
        # create cost tracker and add some costs
        tracker = create_cost_tracker(self.run_id)
        
        from src.cost_tracking.models import CostEntry, CostType
        
        # add sample costs
        assemblyai_cost = CostEntry(
            provider=ProviderType.ASSEMBLYAI,
            cost_type=CostType.AUDIO_DURATION,
            amount=Decimal("1.50"),
            unit_cost=Decimal("0.37"),
            units_consumed=4.05,
        )
        tracker.add_cost_entry(assemblyai_cost)
        
        gemini_cost = CostEntry(
            provider=ProviderType.GEMINI,
            cost_type=CostType.TOKEN_USAGE,
            amount=Decimal("0.25"),
            unit_cost=Decimal("0.000125"),
            units_consumed=2000,
        )
        tracker.add_cost_entry(gemini_cost)
        
        # test cost finalization
        final_summary = self.cost_integration.finalize_job_costs()
        
        assert final_summary is not None
        assert final_summary["total_cost"] == 1.75
        assert final_summary["assemblyai_cost"] == 1.50
        assert final_summary["gemini_cost"] == 0.25
    
    def test_multi_level_time_tracking_integration(self):
        """test multi-level time tracking integration."""
        from src.cost_tracking.processing_tracker import (
            get_multi_level_tracker,
            multi_level_timing_context
        )
        
        # test multi-level timing context
        with multi_level_timing_context(self.run_id) as tracker:
            # simulate task processing
            tracker.track_task_time("demucs", 30.0)
            tracker.track_task_time("diarization", 15.0)
            tracker.track_task_time("transcription", 45.0)
            
            # simulate some processing time
            import time
            time.sleep(0.1)
        
        # get final breakdown
        from src.cost_tracking.processing_tracker import remove_multi_level_tracker
        breakdown = remove_multi_level_tracker(self.run_id)
        
        assert breakdown is not None
        assert breakdown["total_task_time_seconds"] == 90.0
        assert breakdown["total_video_time_seconds"] > 0.1
        assert "demucs" in breakdown["task_breakdown"]
        assert "diarization" in breakdown["task_breakdown"]
        assert "transcription" in breakdown["task_breakdown"]
    
    def test_cost_export_integration(self):
        """test cost data export integration."""
        # enable cost export
        self.config.cost_tracking.export_cost_data = True
        
        # create integration with export enabled
        integration = CostTrackingIntegration(self.config, "export_test_run")
        
        # add some costs
        tracker = create_cost_tracker("export_test_run")
        from src.cost_tracking.models import CostEntry, CostType
        
        cost_entry = CostEntry(
            provider=ProviderType.ASSEMBLYAI,
            cost_type=CostType.AUDIO_DURATION,
            amount=Decimal("1.50"),
            unit_cost=Decimal("0.37"),
            units_consumed=4.05,
        )
        tracker.add_cost_entry(cost_entry)
        
        # test finalization with export
        with patch('pathlib.Path.mkdir'), patch('builtins.open', create=True) as mock_open:
            mock_file = MagicMock()
            mock_open.return_value.__enter__.return_value = mock_file
            
            final_summary = integration.finalize_job_costs()
            
            # verify export was attempted
            assert mock_open.called
            assert mock_file.write.called or hasattr(mock_file, 'write')


class TestCostTrackingPerformanceIntegration:
    """test performance impact of cost tracking on the pipeline."""
    
    def test_cost_tracking_overhead(self):
        """test that cost tracking adds minimal overhead."""
        import time
        
        # test without cost tracking
        start_time = time.time()
        for _ in range(1000):
            # simulate some processing
            pass
        baseline_time = time.time() - start_time
        
        # test with cost tracking
        run_id = "performance_test"
        tracker = create_cost_tracker(run_id)
        
        start_time = time.time()
        for i in range(1000):
            # simulate processing with cost tracking
            from src.cost_tracking.models import CostEntry, CostType
            cost_entry = CostEntry(
                provider=ProviderType.ASSEMBLYAI,
                cost_type=CostType.AUDIO_DURATION,
                amount=Decimal("0.01"),
                unit_cost=Decimal("0.01"),
                units_consumed=1.0,
            )
            tracker.add_cost_entry(cost_entry)
        
        cost_tracking_time = time.time() - start_time
        
        # cost tracking should add less than 50% overhead
        overhead_ratio = cost_tracking_time / max(baseline_time, 0.001)
        assert overhead_ratio < 1.5
        
        # cleanup
        from src.cost_tracking.middleware import remove_cost_tracker
        remove_cost_tracker(run_id)


if __name__ == "__main__":
    pytest.main([__file__])
